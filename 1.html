	
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Premium Weather Cards - Apple Style</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            overflow-x: auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.8;
            font-weight: 300;
        }

        .weather-container {
            display: flex;
            gap: 30px;
            flex-wrap: wrap;
            justify-content: center;
            max-width: 1400px;
        }

        .weather-card {
            width: 300px;
            height: 400px;
            border-radius: 24px;
            position: relative;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .weather-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.4);
        }

        .card-content {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 30px;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
            color: white;
            z-index: 10;
        }

        .weather-type {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .temperature {
            font-size: 3rem;
            font-weight: 300;
            margin-bottom: 5px;
        }

        .description {
            font-size: 1rem;
            opacity: 0.9;
            font-weight: 400;
        }

        /* 晴天卡片 */
        .sunny {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 50%, #fd79a8 100%);
        }

        .sun {
            position: absolute;
            top: 50px;
            right: 50px;
            width: 80px;
            height: 80px;
            background: radial-gradient(circle, #fff 30%, #ffd700 70%);
            border-radius: 50%;
            animation: sunRotate 20s linear infinite;
            box-shadow: 0 0 30px rgba(255, 215, 0, 0.6);
        }

        .sun::before {
            content: '';
            position: absolute;
            top: -20px;
            left: -20px;
            right: -20px;
            bottom: -20px;
            background: radial-gradient(circle, transparent 60%, rgba(255, 215, 0, 0.3) 70%);
            border-radius: 50%;
            animation: sunGlow 3s ease-in-out infinite alternate;
        }

        .sun-rays {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .ray {
            position: absolute;
            width: 2px;
            height: 20px;
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), transparent);
            transform-origin: center bottom;
        }

        @keyframes sunRotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes sunGlow {
            from { opacity: 0.5; }
            to { opacity: 1; }
        }

        /* 雨天卡片 */
        .rainy {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 50%, #2d3436 100%);
        }

        .cloud {
            position: absolute;
            background: #ecf0f1;
            border-radius: 50px;
            opacity: 0.9;
        }

        .cloud::before,
        .cloud::after {
            content: '';
            position: absolute;
            background: #ecf0f1;
            border-radius: 50px;
        }

        .cloud1 {
            width: 80px;
            height: 40px;
            top: 60px;
            left: 40px;
            animation: cloudFloat 8s ease-in-out infinite;
        }

        .cloud1::before {
            width: 50px;
            height: 50px;
            top: -25px;
            left: 10px;
        }

        .cloud1::after {
            width: 60px;
            height: 40px;
            top: -15px;
            right: 10px;
        }

        .cloud2 {
            width: 60px;
            height: 30px;
            top: 40px;
            right: 30px;
            animation: cloudFloat 6s ease-in-out infinite reverse;
        }

        .cloud2::before {
            width: 40px;
            height: 40px;
            top: -20px;
            left: 5px;
        }

        .cloud2::after {
            width: 50px;
            height: 30px;
            top: -10px;
            right: 5px;
        }

        .rain {
            position: absolute;
            width: 2px;
            height: 15px;
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), rgba(116, 185, 255, 0.6));
            border-radius: 1px;
            animation: rainFall 1s linear infinite;
        }

        @keyframes cloudFloat {
            0%, 100% { transform: translateX(0px); }
            50% { transform: translateX(10px); }
        }

        @keyframes rainFall {
            0% {
                transform: translateY(-10px);
                opacity: 1;
            }
            100% {
                transform: translateY(400px);
                opacity: 0;
            }
        }

        /* 雪天卡片 */
        .snowy {
            background: linear-gradient(135deg, #ddd6fe 0%, #a5b4fc 50%, #6366f1 100%);
        }

        .snowflake {
            position: absolute;
            color: white;
            font-size: 1rem;
            animation: snowFall linear infinite;
            opacity: 0.8;
        }

        @keyframes snowFall {
            0% {
                transform: translateY(-10px) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(420px) rotate(360deg);
                opacity: 0;
            }
        }

        /* 风天卡片 */
        .windy {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 50%, #d299c2 100%);
        }

        .wind-line {
            position: absolute;
            height: 2px;
            background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.8), transparent);
            border-radius: 1px;
            animation: windMove 2s ease-in-out infinite;
        }

        .tree {
            position: absolute;
            bottom: 100px;
            left: 50%;
            transform: translateX(-50%);
            width: 8px;
            height: 60px;
            background: #8b4513;
            border-radius: 4px;
            animation: treeSwing 3s ease-in-out infinite;
            transform-origin: bottom center;
        }

        .tree::before {
            content: '';
            position: absolute;
            top: -30px;
            left: -15px;
            width: 38px;
            height: 40px;
            background: #228b22;
            border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
        }

        @keyframes windMove {
            0% {
                transform: translateX(-100px);
                opacity: 0;
            }
            50% {
                opacity: 1;
            }
            100% {
                transform: translateX(400px);
                opacity: 0;
            }
        }

        @keyframes treeSwing {
            0%, 100% { transform: translateX(-50%) rotate(0deg); }
            25% { transform: translateX(-50%) rotate(5deg); }
            75% { transform: translateX(-50%) rotate(-5deg); }
        }

        .controls {
            margin-top: 40px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .control-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .control-btn.active {
            background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        @media (max-width: 768px) {
            .weather-container {
                flex-direction: column;
                align-items: center;
            }
            
            .weather-card {
                width: 280px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Premium Weather</h1>
        <p>Experience weather like never before</p>
    </div>

    <div class="weather-container">
        <!-- 晴天卡片 -->
        <div class="weather-card sunny" data-weather="sunny">
            <div class="sun">
                <div class="sun-rays"></div>
            </div>
            <div class="card-content">
                <div class="weather-type">Sunny</div>
                <div class="temperature">28°</div>
                <div class="description">Perfect day for outdoor activities</div>
            </div>
        </div>

        <!-- 雨天卡片 -->
        <div class="weather-card rainy" data-weather="rainy">
            <div class="cloud cloud1"></div>
            <div class="cloud cloud2"></div>
            <div class="card-content">
                <div class="weather-type">Rainy</div>
                <div class="temperature">18°</div>
                <div class="description">Light rain throughout the day</div>
            </div>
        </div>

        <!-- 雪天卡片 -->
        <div class="weather-card snowy" data-weather="snowy">
            <div class="card-content">
                <div class="weather-type">Snowy</div>
                <div class="temperature">-2°</div>
                <div class="description">Heavy snowfall expected</div>
            </div>
        </div>

        <!-- 风天卡片 -->
        <div class="weather-card windy" data-weather="windy">
            <div class="tree"></div>
            <div class="card-content">
                <div class="weather-type">Windy</div>
                <div class="temperature">22°</div>
                <div class="description">Strong winds up to 25 km/h</div>
            </div>
        </div>
    </div>

    <div class="controls">
        <button class="control-btn active" onclick="showWeather('all')">All Weather</button>
        <button class="control-btn" onclick="showWeather('sunny')">Sunny</button>
        <button class="control-btn" onclick="showWeather('rainy')">Rainy</button>
        <button class="control-btn" onclick="showWeather('snowy')">Snowy</button>
        <button class="control-btn" onclick="showWeather('windy')">Windy</button>
    </div>

    <script>
        // 创建太阳光线
        function createSunRays() {
            const sunRays = document.querySelector('.sun-rays');
            if (!sunRays) return;
            
            sunRays.innerHTML = '';
            for (let i = 0; i < 12; i++) {
                const ray = document.createElement('div');
                ray.className = 'ray';
                ray.style.transform = `rotate(${i * 30}deg)`;
                ray.style.top = '10px';
                ray.style.left = '50%';
                ray.style.marginLeft = '-1px';
                sunRays.appendChild(ray);
            }
        }

        // 创建雨滴
        function createRain() {
            const rainyCard = document.querySelector('.rainy');
            if (!rainyCard) return;

            // 清除现有雨滴
            rainyCard.querySelectorAll('.rain').forEach(drop => drop.remove());

            for (let i = 0; i < 50; i++) {
                const rain = document.createElement('div');
                rain.className = 'rain';
                rain.style.left = Math.random() * 100 + '%';
                rain.style.animationDelay = Math.random() * 2 + 's';
                rain.style.animationDuration = (Math.random() * 0.5 + 0.5) + 's';
                rainyCard.appendChild(rain);
            }
        }

        // 创建雪花
        function createSnow() {
            const snowyCard = document.querySelector('.snowy');
            if (!snowyCard) return;

            // 清除现有雪花
            snowyCard.querySelectorAll('.snowflake').forEach(flake => flake.remove());

            const snowflakeSymbols = ['❄', '❅', '❆', '✻', '✼', '❋'];
            
            for (let i = 0; i < 30; i++) {
                const snowflake = document.createElement('div');
                snowflake.className = 'snowflake';
                snowflake.textContent = snowflakeSymbols[Math.floor(Math.random() * snowflakeSymbols.length)];
                snowflake.style.left = Math.random() * 100 + '%';
                snowflake.style.animationDelay = Math.random() * 3 + 's';
                snowflake.style.animationDuration = (Math.random() * 3 + 2) + 's';
                snowflake.style.fontSize = (Math.random() * 0.8 + 0.8) + 'rem';
                snowyCard.appendChild(snowflake);
            }
        }

        // 创建风线
        function createWindLines() {
            const windyCard = document.querySelector('.windy');
            if (!windyCard) return;

            // 清除现有风线
            windyCard.querySelectorAll('.wind-line').forEach(line => line.remove());

            for (let i = 0; i < 8; i++) {
                const windLine = document.createElement('div');
                windLine.className = 'wind-line';
                windLine.style.top = (20 + i * 30) + 'px';
                windLine.style.width = (Math.random() * 100 + 50) + 'px';
                windLine.style.animationDelay = Math.random() * 2 + 's';
                windLine.style.animationDuration = (Math.random() * 1 + 1.5) + 's';
                windyCard.appendChild(windLine);
            }
        }

        // 显示特定天气
        function showWeather(type) {
            const cards = document.querySelectorAll('.weather-card');
            const buttons = document.querySelectorAll('.control-btn');
            
            // 更新按钮状态
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            if (type === 'all') {
                cards.forEach(card => {
                    card.style.display = 'block';
                    card.style.opacity = '1';
                    card.style.transform = 'scale(1)';
                });
            } else {
                cards.forEach(card => {
                    if (card.dataset.weather === type) {
                        card.style.display = 'block';
                        card.style.opacity = '1';
                        card.style.transform = 'scale(1.05)';
                    } else {
                        card.style.opacity = '0.3';
                        card.style.transform = 'scale(0.95)';
                    }
                });
            }
        }

        // 添加卡片点击效果
        function addCardInteractions() {
            const cards = document.querySelectorAll('.weather-card');
            
            cards.forEach(card => {
                card.addEventListener('click', function() {
                    // 添加点击波纹效果
                    const ripple = document.createElement('div');
                    ripple.style.position = 'absolute';
                    ripple.style.borderRadius = '50%';
                    ripple.style.background = 'rgba(255, 255, 255, 0.3)';
                    ripple.style.transform = 'scale(0)';
                    ripple.style.animation = 'ripple 0.6s linear';
                    ripple.style.left = '50%';
                    ripple.style.top = '50%';
                    ripple.style.width = '20px';
                    ripple.style.height = '20px';
                    ripple.style.marginLeft = '-10px';
                    ripple.style.marginTop = '-10px';
                    
                    this.appendChild(ripple);
                    
                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
        }

        // 添加波纹动画CSS
        const rippleStyle = document.createElement('style');
        rippleStyle.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(rippleStyle);

        // 初始化所有动画
        function initializeAnimations() {
            createSunRays();
            createRain();
            createSnow();
            createWindLines();
            addCardInteractions();
        }

        // 定期刷新动画以保持流畅
        function refreshAnimations() {
            setInterval(() => {
                createRain();
                createSnow();
                createWindLines();
            }, 5000);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeAnimations();
            refreshAnimations();
            
            // 添加页面可见性检测，优化性能
            document.addEventListener('visibilitychange', function() {
                if (document.hidden) {
                    // 页面隐藏时暂停动画
                    document.querySelectorAll('.weather-card *').forEach(el => {
                        el.style.animationPlayState = 'paused';
                    });
                } else {
                    // 页面显示时恢复动画
                    document.querySelectorAll('.weather-card *').forEach(el => {
                        el.style.animationPlayState = 'running';
                    });
                }
            });
        });

        // 添加键盘快捷键支持
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case '1':
                    showWeather('all');
                    break;
                case '2':
                    showWeather('sunny');
                    break;
                case '3':
                    showWeather('rainy');
                    break;
                case '4':
                    showWeather('snowy');
                    break;
                case '5':
                    showWeather('windy');
                    break;
            }
        });
    </script>
</body>
</html>