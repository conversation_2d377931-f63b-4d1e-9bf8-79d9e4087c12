# Cherry Studio URL Scheme 生成原理

## 概述

Cherry Studio 使用自定义 URL Scheme 来快速导入 API 配置。本文档详细说明了如何生成这些链接的技术原理，方便开发者集成到自己的应用中。

## 核心原理

### 1. 配置对象结构

cherrystudio://providers/api-keys?v=1&data={cherryConfig}
cherryConfig配置使用以下 JSON 结构：
```json
{
  "id": "new-api",
  "baseUrl": "https://api.openai.com/v1",
  "apiKey": "sk-1234567890abcdefghijklmnopqrstuvwxyz123456"
}
```
**字段说明：**
- `id`: 通常使用 `"new-api"`
- `baseUrl`: API 服务器地址
- `apiKey`: API 密钥

URL Scheme 的生成需要经过三个编码步骤：

```
JSON配置 → Base64编码  → 嵌入URL Scheme
```


#### 步骤详解：

**步骤 1: 创建 JSON 配置**
```javascript
const cherryConfig = {
    id: 'new-api',
    baseUrl: serverAddress,
    apiKey: apiKey
};
const configJson = JSON.stringify(cherryConfig);
```

**步骤 2: Base64 编码**
```javascript
const base64Config = btoa(configJson);
```

**步骤 3: URL 编码**
```javascript
const encodedConfig = encodeURIComponent(base64Config);
```

**步骤 4: 生成最终 URL Scheme**
```javascript
const urlScheme = `cherrystudio://providers/api-keys?v=1&data=${encodedConfig}`;
```

### 3. URL Scheme 格式

最终生成的 URL Scheme 格式为：

```
cherrystudio://providers/api-keys?v=1&data={编码后的配置数据}
```

**参数说明：**
- `cherrystudio://`: Cherry Studio 的自定义协议
- `providers/api-keys`: 路径，表示 API 密钥配置
- `v=1`: 版本号
- `data`: Base64 + URL 编码后的配置数据

## 实现示例

### JavaScript 实现

```javascript
function generateCherryStudioUrl(baseUrl, apiKey) {
    // 1. 创建配置对象
    const config = {
        id: 'new-api',
        baseUrl: baseUrl,
        apiKey: apiKey
    };
    
    // 2. 转换为 JSON 字符串
    const jsonString = JSON.stringify(config);
    
    // 3. Base64 编码
    const base64String = btoa(jsonString);
    
    // 4. URL 编码
    const urlEncoded = encodeURIComponent(base64String);
    
    // 5. 生成最终 URL
    const urlScheme = `cherrystudio://providers/api-keys?v=1&data=${urlEncoded}`;
    
    return urlScheme;
}

// 使用示例
const url = generateCherryStudioUrl(
    'https://api.openai.com/v1',
    'sk-1234567890abcdefghijklmnopqrstuvwxyz123456'
);
console.log(url);
```

### Python 实现

```python
import json
import base64
import urllib.parse

def generate_cherry_studio_url(base_url, api_key):
    # 1. 创建配置对象
    config = {
        'id': 'new-api',
        'baseUrl': base_url,
        'apiKey': api_key
    }
    
    # 2. 转换为 JSON 字符串
    json_string = json.dumps(config)
    
    # 3. Base64 编码
    base64_bytes = base64.b64encode(json_string.encode('utf-8'))
    base64_string = base64_bytes.decode('utf-8')
    
    # 4. URL 编码
    url_encoded = urllib.parse.quote(base64_string)
    
    # 5. 生成最终 URL
    url_scheme = f'cherrystudio://providers/api-keys?v=1&data={url_encoded}'
    
    return url_scheme

# 使用示例
url = generate_cherry_studio_url(
    'https://api.openai.com/v1',
    'sk-1234567890abcdefghijklmnopqrstuvwxyz123456'
)
print(url)
```

### Go 实现

```go
package main

import (
    "encoding/base64"
    "encoding/json"
    "fmt"
    "net/url"
)

type CherryConfig struct {
    ID      string `json:"id"`
    BaseURL string `json:"baseUrl"`
    APIKey  string `json:"apiKey"`
}

func GenerateCherryStudioURL(baseURL, apiKey string) (string, error) {
    // 1. 创建配置对象
    config := CherryConfig{
        ID:      "new-api",
        BaseURL: baseURL,
        APIKey:  apiKey,
    }
    
    // 2. 转换为 JSON
    jsonBytes, err := json.Marshal(config)
    if err != nil {
        return "", err
    }
    
    // 3. Base64 编码
    base64String := base64.StdEncoding.EncodeToString(jsonBytes)
    
    // 4. URL 编码
    urlEncoded := url.QueryEscape(base64String)
    
    // 5. 生成最终 URL
    urlScheme := fmt.Sprintf("cherrystudio://providers/api-keys?v=1&data=%s", urlEncoded)
    
    return urlScheme, nil
}

func main() {
    url, err := GenerateCherryStudioURL(
        "https://api.openai.com/v1",
        "sk-1234567890abcdefghijklmnopqrstuvwxyz123456",
    )
    if err != nil {
        panic(err)
    }
    fmt.Println(url)
}
```

## 支持的 API 密钥格式

Cherry Studio 支持多种 API 密钥格式：

| 服务商 | 密钥格式 | 示例 |
|--------|----------|------|
| OpenAI | `sk-...` | `sk-1234567890abcdefghijklmnopqrstuvwxyz123456` |
| Anthropic | `sk-ant-...` | `sk-ant-api03-1234567890abcdef...` |
| Google AI | `AIza...` | `AIzaSyC1234567890abcdefghijklmnopqrstuvwxyz` |
| Hugging Face | `hf_...` | `hf_1234567890abcdefghijklmnopqrstuvwxyz` |
| Azure OpenAI | 32位十六进制 | `a1b2c3d4e5f6789012345678901234567` |
| JWT Token | `eyJ...` | `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` |

## 验证和错误处理

### 输入验证

在生成 URL Scheme 之前，建议进行以下验证：

```javascript
function validateInputs(baseUrl, apiKey) {
    // 验证 URL 格式
    try {
        new URL(baseUrl);
    } catch (e) {
        throw new Error('无效的服务器地址格式');
    }
    
    // 验证 API 密钥
    if (!apiKey || apiKey.length < 10) {
        throw new Error('API 密钥过短');
    }
    
    return true;
}
```

### 常见错误

1. **Base64 编码错误**: 确保 JSON 字符串格式正确
2. **URL 编码错误**: 使用正确的 URL 编码函数
3. **配置格式错误**: 确保 JSON 对象包含必需字段

## 测试用例

### 基本测试

```javascript
// 测试用例 1: OpenAI 配置
const testCase1 = {
    baseUrl: 'https://api.openai.com/v1',
    apiKey: 'sk-1234567890abcdefghijklmnopqrstuvwxyz123456'
};

// 测试用例 2: 自定义 API
const testCase2 = {
    baseUrl: 'https://my-api.example.com:8080/v1',
    apiKey: 'sk-custom1234567890abcdefghijklmnop'
};

// 测试用例 3: 本地服务
const testCase3 = {
    baseUrl: 'http://localhost:8000',
    apiKey: 'sk-local1234567890abcdefghijklmnop'
};
```

### 解码验证

生成的 URL 可以通过反向操作验证：

```javascript
function decodeCherryStudioUrl(urlScheme) {
    // 1. 提取 data 参数
    const url = new URL(urlScheme);
    const encodedData = url.searchParams.get('data');
    
    // 2. URL 解码
    const base64Data = decodeURIComponent(encodedData);
    
    // 3. Base64 解码
    const jsonString = atob(base64Data);
    
    // 4. 解析 JSON
    const config = JSON.parse(jsonString);
    
    return config;
}
```

## 注意事项

1. **字符编码**: 确保使用 UTF-8 编码处理中文等特殊字符
2. **URL 长度限制**: 某些系统对 URL 长度有限制，注意配置数据的大小
3. **安全性**: API 密钥会包含在 URL 中，注意传输安全
4. **版本兼容性**: 当前使用 `v=1`，未来可能有版本更新

## 集成建议

1. **错误处理**: 实现完整的错误处理和用户反馈
2. **输入验证**: 在生成前验证输入数据的有效性
3. **用户体验**: 提供复制链接、预览配置等功能
4. **测试**: 使用多种配置进行充分测试

这个文档提供了完整的技术实现细节，开发者可以根据自己的编程语言和需求进行相应的实现。
