<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL Scheme 生成器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 700px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .header p {
            opacity: 0.9;
            font-size: 16px;
        }

        .form-container {
            padding: 30px;
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-group label {
            display: block;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .form-group input, .form-group textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f9fafb;
        }

        .form-group input:focus, .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
            font-family: monospace;
        }

        .button-group {
            display: flex;
            gap: 12px;
            margin-bottom: 24px;
        }

        .btn {
            flex: 1;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 2px solid #d1d5db;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
            border-color: #9ca3af;
        }

        .result-container {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin-top: 24px;
        }

        .result-container.success {
            background: #f0fdf4;
            border-color: #22c55e;
        }

        .result-section {
            margin-bottom: 24px;
        }

        .result-section:last-child {
            margin-bottom: 0;
        }

        .result-title {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 12px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .result-content {
            font-family: monospace;
            background: #ffffff;
            border: 1px solid #cbd5e1;
            border-radius: 6px;
            padding: 12px;
            word-break: break-all;
            line-height: 1.5;
            color: #1e293b;
            position: relative;
        }

        .result-content.expandable {
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .result-content.expandable:hover {
            background: #f8fafc;
        }

        .copy-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #f1f5f9;
            border: 1px solid #cbd5e1;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .result-content:hover .copy-btn {
            opacity: 1;
        }

        .copy-btn:hover {
            background: #e2e8f0;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #22c55e;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .error {
            color: #ef4444;
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 6px;
            padding: 12px;
            margin-top: 12px;
            font-size: 14px;
        }

        .info-card {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
            font-size: 14px;
            color: #1e40af;
        }

        .auto-detect {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 6px;
            padding: 8px 12px;
            margin-top: 8px;
            font-size: 12px;
            color: #0c4a6e;
        }

        .config-preview {
            background: #1e293b;
            color: #e2e8f0;
            border-radius: 6px;
            padding: 16px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 13px;
            line-height: 1.4;
            overflow-x: auto;
            white-space: pre;
        }

        .decode-info {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 6px;
            padding: 12px;
            margin-top: 8px;
            font-size: 13px;
            color: #92400e;
        }

        .tabs {
            display: flex;
            border-bottom: 2px solid #e5e7eb;
            margin-bottom: 16px;
        }

        .tab {
            padding: 10px 16px;
            background: none;
            border: none;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            color: #6b7280;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .tab.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        @media (max-width: 640px) {
            .button-group {
                flex-direction: column;
            }
            
            .container {
                margin: 10px;
            }
            
            .form-container {
                padding: 20px;
            }

            .tabs {
                flex-wrap: wrap;
            }

            .tab {
                flex: 1;
                min-width: 120px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 URL Scheme 生成器</h1>
            <p>自动识别服务器地址和 API 密钥，生成 Cherry Studio 配置链接</p>
        </div>

        <div class="form-container">
            <div class="info-card">
                <strong>💡 使用说明：</strong> 输入包含服务器地址和 API 密钥的文本，程序会自动识别并生成对应的 URL Scheme。
            </div>

            <div class="form-group">
                <label for="input-text">📝 输入文本</label>
                <textarea 
                    id="input-text" 
                    placeholder="请输入包含服务器地址和API密钥的文本，例如：
服务器：https://api.example.com
密钥：sk-1234567890abcdef
或者任何包含这些信息的文本..."
                    oninput="autoDetect()"
                ></textarea>
                <div class="auto-detect" id="auto-detect" style="display: none;"></div>
            </div>

            <div class="form-group">
                <label for="server-address">🌐 服务器地址</label>
                <input 
                    type="url" 
                    id="server-address" 
                    placeholder="https://api.example.com"
                    oninput="updateResult()"
                >
            </div>

            <div class="form-group">
                <label for="api-key">🔑 API 密钥</label>
                <input 
                    type="text" 
                    id="api-key" 
                    placeholder="sk-1234567890abcdef"
                    oninput="updateResult()"
                >
            </div>

            <div class="button-group">
                <button class="btn btn-primary" onclick="generateUrlScheme()">
                    🚀 生成 URL Scheme
                </button>
                <button class="btn btn-secondary" onclick="clearAll()">
                    🗑️ 清空
                </button>
            </div>

            <div id="result-container" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 全局变量存储当前配置
        let currentConfig = null;
        let currentUrlScheme = '';

        // 自动检测输入文本中的服务器地址和API密钥
        function autoDetect() {
            const inputText = document.getElementById('input-text').value;
            const autoDetectDiv = document.getElementById('auto-detect');
            
            if (!inputText.trim()) {
                autoDetectDiv.style.display = 'none';
                return;
            }

            let serverAddress = '';
            let apiKey = '';
            let detectedInfo = [];

            // 检测服务器地址 - 多种模式
            const urlPatterns = [
                /https?:\/\/[^\s\n\r<>"']+/gi,  // 标准URL
                /(?:服务器|server|base_?url|endpoint)[\s\:：]*([^\s\n\r]+)/gi,  // 带标签的URL
            ];

            for (const pattern of urlPatterns) {
                const matches = inputText.match(pattern);
                if (matches) {
                    serverAddress = matches[0];
                    // 清理可能的标签
                    serverAddress = serverAddress.replace(/^[^h]*https?/i, 'http').trim();
                    if (serverAddress.match(/^https?:\/\//)) {
                        detectedInfo.push(`🌐 服务器: ${serverAddress}`);
                        break;
                    }
                }
            }

            // 检测API密钥 - 多种模式
            const keyPatterns = [
                /sk-[a-zA-Z0-9]{20,}/g,  // OpenAI格式
                /[a-zA-Z0-9]{32,}/g,     // 通用长密钥
                /(?:密钥|key|token|api_?key)[\s\:：]*([a-zA-Z0-9\-_]{20,})/gi  // 带标签的密钥
            ];

            for (const pattern of keyPatterns) {
                const matches = inputText.match(pattern);
                if (matches) {
                    apiKey = matches[0];
                    // 清理可能的标签
                    if (pattern.source.includes('密钥|key|token')) {
                        const keyMatch = apiKey.match(/[a-zA-Z0-9\-_]{20,}/);
                        if (keyMatch) apiKey = keyMatch[0];
                    }
                    
                    // 确保是sk-格式
                    if (!apiKey.startsWith('sk-') && apiKey.length > 20) {
                        apiKey = 'sk-' + apiKey;
                    }
                    
                    if (apiKey.length >= 23) {  // sk- + 20字符
                        detectedInfo.push(`🔑 密钥: ${apiKey.substring(0, 10)}...${apiKey.substring(apiKey.length - 6)}`);
                        break;
                    }
                }
            }

            if (detectedInfo.length > 0) {
                autoDetectDiv.innerHTML = `🔍 自动识别: ${detectedInfo.join(', ')}`;
                autoDetectDiv.style.display = 'block';
                
                // 自动填充到表单
                if (serverAddress) {
                    document.getElementById('server-address').value = serverAddress;
                }
                if (apiKey) {
                    document.getElementById('api-key').value = apiKey;
                }
                
                // 自动更新结果
                updateResult();
            } else {
                autoDetectDiv.innerHTML = '❌ 未识别到有效的服务器地址或API密钥';
                autoDetectDiv.style.display = 'block';
            }
        }

        // 实时更新结果
        function updateResult() {
            const serverAddress = document.getElementById('server-address').value.trim();
            const apiKey = document.getElementById('api-key').value.trim();
            
            if (serverAddress && apiKey) {
                generateUrlScheme();
            }
        }

        // 生成URL Scheme
        function generateUrlScheme() {
            const serverAddress = document.getElementById('server-address').value.trim();
            const apiKey = document.getElementById('api-key').value.trim();
            const resultContainer = document.getElementById('result-container');

            // 验证输入
            if (!serverAddress || !apiKey) {
                showError('请填写完整的服务器地址和API密钥');
                return;
            }

            // 验证URL格式
            try {
                new URL(serverAddress);
            } catch (e) {
                showError('服务器地址格式不正确，请使用完整的URL（如：https://api.example.com）');
                return;
            }

            // 验证API密钥格式
            if (!apiKey.startsWith('sk-') || apiKey.length < 23) {
                showError('API密钥格式不正确，应该以 "sk-" 开头并包含足够的字符');
                return;
            }

            try {
                // 创建配置对象
                const cherryConfig = {
                    id: 'new-api',
                    baseUrl: serverAddress,
                    apiKey: apiKey
                };

                // JSON字符串
                const configJson = JSON.stringify(cherryConfig);
                
                // Base64编码
                const base64Config = btoa(configJson);
                
                // URL编码
                const encodedConfig = encodeURIComponent(base64Config);

                // 生成URL Scheme
                const urlScheme = `cherrystudio://providers/api-keys?v=1&data=${encodedConfig}`;
                
                // 生成编码前的URL
                const urlSchemeBeforeBase64 = `cherrystudio://providers/api-keys?v=1&data=${configJson}`;

                // 保存到全局变量
                currentConfig = cherryConfig;
                currentUrlScheme = urlScheme;

                // 显示结果
                displayResults(urlScheme, urlSchemeBeforeBase64, configJson, base64Config, encodedConfig);

            } catch (error) {
                showError('生成URL Scheme时发生错误: ' + error.message);
            }
        }

        // 显示生成结果
        function displayResults(finalUrl, beforeBase64Url, jsonString, base64String, encodedString) {
            const resultContainer = document.getElementById('result-container');
            
            resultContainer.innerHTML = `
                <div class="result-title">
                    <span class="status-indicator"></span>
                    ✅ URL Scheme 生成成功
                </div>

                <!-- 标签页 -->
                <div class="tabs">
                    <button class="tab active" onclick="switchTab('final')">🎯 最终结果</button>
                    <button class="tab" onclick="switchTab('original')">📝 编码前内容</button>
                    <button class="tab" onclick="switchTab('process')">🔧 编码过程</button>
                    <button class="tab" onclick="switchTab('config')">⚙️ 配置详情</button>
                </div>

                <!-- 最终URL Scheme -->
                <div id="tab-final" class="tab-content active">
                    <div class="result-section">
                        <div class="result-title">🚀 最终 URL Scheme（可直接使用）</div>
                        <div class="result-content" onclick="selectText(this)">
                            <span class="copy-btn" onclick="event.stopPropagation(); copyToClipboard('${finalUrl.replace(/'/g, "\\'")}', '最终URL')">复制</span>
                            ${finalUrl}
                        </div>
                        <div class="decode-info">
                            💡 此链接包含Base64编码的配置信息，可直接在Cherry Studio中使用
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div style="display: flex; gap: 8px; margin-top: 16px; flex-wrap: wrap;">
                        <button class="btn btn-primary" style="flex: none; padding: 8px 16px; font-size: 14px;" onclick="copyToClipboard('${finalUrl.replace(/'/g, "\\'")}', '最终URL')">
                            📋 复制链接
                        </button>
                        <button class="btn btn-secondary" style="flex: none; padding: 8px 16px; font-size: 14px;" onclick="openUrlScheme('${finalUrl.replace(/'/g, "\\'")}')">
                            🚀 打开应用
                        </button>
                        <button class="btn btn-secondary" style="flex: none; padding: 8px 16px; font-size: 14px;" onclick="validateConfig()">
                            ✅ 验证配置
                        </button>
                    </div>
                </div>

                <!-- 编码前的URL -->
                <div id="tab-original" class="tab-content">
                    <div class="result-section">
                        <div class="result-title">📝 编码前的 URL Scheme（便于理解）</div>
                        <div class="result-content" onclick="selectText(this)">
                            <span class="copy-btn" onclick="event.stopPropagation(); copyToClipboard('${beforeBase64Url.replace(/'/g, "\\'").replace(/"/g, '\\"')}', '编码前URL')">复制</span>
                            ${escapeHtml(beforeBase64Url)}
                        </div>
                        <div class="decode-info">
                            💡 这是未经Base64编码的URL，可以直接看到JSON配置内容，但Cherry Studio无法直接使用
                        </div>
                    </div>
                </div>

                <!-- 编码过程 -->
                <div id="tab-process" class="tab-content">
                    <div class="result-section">
                        <div class="result-title">🔧 编码转换过程</div>
                        
                        <div style="margin-bottom: 16px;">
                            <strong>步骤 1: JSON 配置</strong>
                            <div class="result-content" onclick="selectText(this)">
                                <span class="copy-btn" onclick="event.stopPropagation(); copyToClipboard('${jsonString.replace(/'/g, "\\'")}', 'JSON配置')">复制</span>
                                ${escapeHtml(jsonString)}
                            </div>
                        </div>

                        <div style="margin-bottom: 16px;">
                            <strong>步骤 2: Base64 编码</strong>
                            <div class="result-content" onclick="selectText(this)" style="word-break: break-all;">
                                <span class="copy-btn" onclick="event.stopPropagation(); copyToClipboard('${base64String}', 'Base64编码')">复制</span>
                                ${base64String}
                            </div>
                        </div>

                        <div style="margin-bottom: 16px;">
                            <strong>步骤 3: URL 编码</strong>
                            <div class="result-content" onclick="selectText(this)" style="word-break: break-all;">
                                <span class="copy-btn" onclick="event.stopPropagation(); copyToClipboard('${encodedString}', 'URL编码')">复制</span>
                                ${encodedString}
                            </div>
                        </div>

                        <div class="decode-info">
                            🔄 编码流程: JSON → Base64 → URL编码 → 嵌入URL Scheme
                        </div>
                    </div>
                </div>

                <!-- 配置详情 -->
                <div id="tab-config" class="tab-content">
                    <div class="result-section">
                        <div class="result-title">⚙️ Cherry Studio 配置详情</div>
                        <div class="config-preview">${JSON.stringify(currentConfig, null, 2)}</div>
                        <div class="decode-info">
                            📋 此配置将在 Cherry Studio 中创建一个名为 "new-api" 的 API 提供商
                        </div>
                    </div>
                </div>
            `;
            
            resultContainer.className = 'result-container success';
            resultContainer.style.display = 'block';
        }

        // 切换标签页
        function switchTab(tabName) {
            // 移除所有活动状态
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            
            // 激活选中的标签页
            document.querySelector(`button[onclick="switchTab('${tabName}')"]`).classList.add('active');
            document.getElementById(`tab-${tabName}`).classList.add('active');
        }

        // HTML转义
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 选择文本
        function selectText(element) {
            const range = document.createRange();
            range.selectNodeContents(element);
            const selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(range);
        }

        // 验证配置
        function validateConfig() {
            if (!currentConfig) return;
            
            const checks = [
                {
                    name: '服务器地址格式',
                    result: currentConfig.baseUrl.startsWith('http'),
                    message: currentConfig.baseUrl.startsWith('http') ? '✅ 格式正确' : '❌ 缺少协议前缀'
                },
                {
                    name: 'API 密钥格式',
                    result: currentConfig.apiKey.startsWith('sk-') && currentConfig.apiKey.length >= 23,
                    message: (currentConfig.apiKey.startsWith('sk-') && currentConfig.apiKey.length >= 23) ? 
                        '✅ 格式正确' : '❌ 格式不符合要求'
                },
                {
                    name: '配置完整性',
                    result: currentConfig.id && currentConfig.baseUrl && currentConfig.apiKey,
                    message: (currentConfig.id && currentConfig.baseUrl && currentConfig.apiKey) ? 
                        '✅ 配置完整' : '❌ 配置缺失'
                }
            ];
            
            const checkResults = checks.map(check => 
                `<div style="margin-bottom: 8px;"><strong>${check.name}:</strong> ${check.message}</div>`
            ).join('');
            
            showToast('🔍 配置验证完成', checkResults, 5000);
        }

        // 显示错误信息
        function showError(message) {
            const resultContainer = document.getElementById('result-container');
            resultContainer.innerHTML = `
                <div class="result-title">❌ 生成失败</div>
                <div class="error">${message}</div>
            `;
            resultContainer.className = 'result-container';
            resultContainer.style.display = 'block';
        }

        // 复制到剪贴板
        async function copyToClipboard(text, type = '内容') {
            try {
                await navigator.clipboard.writeText(text);
                showToast(`✅ ${type}已复制到剪贴板`);
            } catch (err) {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.opacity = '0';
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showToast(`✅ ${type}已复制到剪贴板`);
            }
        }

        // 打开URL Scheme
        function openUrlScheme(url) {
            window.open(url, '_blank');
            showToast('🚀 正在尝试打开 Cherry Studio...');
        }

        // 清空所有输入
        function clearAll() {
            document.getElementById('input-text').value = '';
            document.getElementById('server-address').value = '';
            document.getElementById('api-key').value = '';
            document.getElementById('result-container').style.display = 'none';
            document.getElementById('auto-detect').style.display = 'none';
            currentConfig = null;
            currentUrlScheme = '';
            showToast('🗑️ 已清空所有内容');
        }

        // 显示提示信息
        function showToast(message, details = '', duration = 3000) {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #1f2937;
                color: white;
                padding: 16px 24px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 500;
                z-index: 1000;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
                max-width: 400px;
                line-height: 1.4;
            `;
            
            let content = message;
            if (details) {
                content += `<div style="margin-top: 8px; font-size: 12px; opacity: 0.9;">${details}</div>`;
            }
            
            toast.innerHTML = content;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(0)';
            }, 100);

            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => document.body.removeChild(toast), 300);
            }, duration);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加示例数据按钮
            const exampleButton = document.createElement('button');
            exampleButton.className = 'btn btn-secondary';
            exampleButton.style.cssText = 'margin-bottom: 16px; padding: 8px 16px; font-size: 12px;';
            exampleButton.textContent = '📋 填入示例数据';
            exampleButton.onclick = function() {
                document.getElementById('input-text').value = `API配置信息：
服务器地址：https://api.openai.com
API密钥：sk-1234567890abcdefghijklmnopqrstuvwxyz123456
连接测试：正常`;
                autoDetect();
            };
            
            document.querySelector('.form-container').insertBefore(exampleButton, document.querySelector('.form-group'));
        });
    </script>
</body>
</html>
