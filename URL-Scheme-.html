<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL Scheme 生成器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 700px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .header p {
            opacity: 0.9;
            font-size: 16px;
        }

        .form-container {
            padding: 30px;
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-group label {
            display: block;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .form-group input, .form-group textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f9fafb;
        }

        .form-group input:focus, .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
            font-family: monospace;
        }

        .button-group {
            display: flex;
            gap: 12px;
            margin-bottom: 24px;
        }

        .btn {
            flex: 1;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 2px solid #d1d5db;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
            border-color: #9ca3af;
        }

        .result-container {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin-top: 24px;
        }

        .result-container.success {
            background: #f0fdf4;
            border-color: #22c55e;
        }

        .result-section {
            margin-bottom: 24px;
        }

        .result-section:last-child {
            margin-bottom: 0;
        }

        .result-title {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 12px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .result-content {
            font-family: monospace;
            background: #ffffff;
            border: 1px solid #cbd5e1;
            border-radius: 6px;
            padding: 12px;
            word-break: break-all;
            line-height: 1.5;
            color: #1e293b;
            position: relative;
        }

        .result-content.expandable {
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .result-content.expandable:hover {
            background: #f8fafc;
        }

        .copy-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #f1f5f9;
            border: 1px solid #cbd5e1;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .result-content:hover .copy-btn {
            opacity: 1;
        }

        .copy-btn:hover {
            background: #e2e8f0;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #22c55e;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .error {
            color: #ef4444;
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 6px;
            padding: 12px;
            margin-top: 12px;
            font-size: 14px;
        }

        .info-card {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
            font-size: 14px;
            color: #1e40af;
        }

        .auto-detect {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 6px;
            padding: 8px 12px;
            margin-top: 8px;
            font-size: 12px;
            color: #0c4a6e;
        }

        .config-preview {
            background: #1e293b;
            color: #e2e8f0;
            border-radius: 6px;
            padding: 16px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 13px;
            line-height: 1.4;
            overflow-x: auto;
            white-space: pre;
        }

        .decode-info {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 6px;
            padding: 12px;
            margin-top: 8px;
            font-size: 13px;
            color: #92400e;
        }

        .tabs {
            display: flex;
            border-bottom: 2px solid #e5e7eb;
            margin-bottom: 16px;
        }

        .tab {
            padding: 10px 16px;
            background: none;
            border: none;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            color: #6b7280;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .tab.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        @media (max-width: 640px) {
            .button-group {
                flex-direction: column;
            }
            
            .container {
                margin: 10px;
            }
            
            .form-container {
                padding: 20px;
            }

            .tabs {
                flex-wrap: wrap;
            }

            .tab {
                flex: 1;
                min-width: 120px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 URL Scheme 生成器</h1>
            <p>自动识别服务器地址和 API 密钥，生成 Cherry Studio 配置链接</p>
        </div>

        <div class="form-container">
            <div class="info-card">
                <strong>💡 使用说明：</strong> 输入包含服务器地址和 API 密钥的文本，程序会自动识别并生成对应的 URL Scheme。
                <br><br>
                <strong>🎯 支持格式：</strong>
                <ul style="margin: 8px 0 0 20px; font-size: 13px;">
                    <li>JSON配置文件 ({"baseUrl": "...", "apiKey": "..."})</li>
                    <li>YAML格式 (baseUrl: https://..., apiKey: sk-...)</li>
                    <li>环境变量格式 (API_URL=https://..., API_KEY=sk-...)</li>
                    <li>纯文本描述 (服务器：https://..., 密钥：sk-...)</li>
                    <li>多组配置自动识别</li>
                </ul>
            </div>

            <div class="form-group">
                <label for="input-text">📝 输入文本</label>
                <textarea
                    id="input-text"
                    placeholder="请输入包含服务器地址和API密钥的文本，例如：

📋 JSON格式：
{&quot;baseUrl&quot;: &quot;https://api.openai.com&quot;, &quot;apiKey&quot;: &quot;sk-...&quot;}

📋 YAML格式：
baseUrl: https://api.openai.com
apiKey: sk-...

📋 纯文本：
服务器：https://api.openai.com
密钥：sk-1234567890abcdef

支持多组配置自动识别！"
                    oninput="autoDetect()"
                    onfocus="showInputTips()"
                    onblur="hideInputTips()"
                ></textarea>
                <div id="input-tips" style="display: none; margin-top: 8px; padding: 8px; background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 6px; font-size: 12px; color: #0c4a6e;">
                    💡 <strong>快速提示：</strong> 可以直接粘贴配置文件内容，支持JSON、YAML、环境变量等多种格式
                </div>
                <div class="auto-detect" id="auto-detect" style="display: none;"></div>
            </div>

            <div class="form-group">
                <label for="server-address">🌐 服务器地址</label>
                <input 
                    type="url" 
                    id="server-address" 
                    placeholder="https://api.example.com"
                    oninput="updateResult()"
                >
            </div>

            <div class="form-group">
                <label for="api-key">🔑 API 密钥</label>
                <input 
                    type="text" 
                    id="api-key" 
                    placeholder="sk-1234567890abcdef"
                    oninput="updateResult()"
                >
            </div>

            <div class="button-group">
                <button class="btn btn-primary" onclick="generateUrlScheme()">
                    🚀 生成 URL Scheme
                </button>
                <button class="btn btn-secondary" onclick="clearAll()">
                    🗑️ 清空
                </button>
            </div>

            <div id="result-container" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 全局变量存储当前配置
        let currentConfig = null;
        let currentUrlScheme = '';

        // 智能文本解析函数
        function parseStructuredText(text) {
            let parsedData = {};

            // 尝试解析JSON
            try {
                parsedData = JSON.parse(text);
                return { type: 'json', data: parsedData };
            } catch (e) {
                // 不是JSON，继续尝试其他格式
            }

            // 尝试解析YAML风格的文本
            const yamlPattern = /^(\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:\s*(.+)$/gm;
            let yamlMatch;
            const yamlData = {};
            while ((yamlMatch = yamlPattern.exec(text)) !== null) {
                const key = yamlMatch[2].toLowerCase();
                const value = yamlMatch[3].trim().replace(/^["']|["']$/g, '');
                yamlData[key] = value;
            }

            if (Object.keys(yamlData).length > 0) {
                return { type: 'yaml', data: yamlData };
            }

            // 尝试解析配置文件格式
            const configPattern = /^([a-zA-Z_][a-zA-Z0-9_]*)\s*[=:]\s*(.+)$/gm;
            let configMatch;
            const configData = {};
            while ((configMatch = configPattern.exec(text)) !== null) {
                const key = configMatch[1].toLowerCase();
                const value = configMatch[2].trim().replace(/^["']|["']$/g, '');
                configData[key] = value;
            }

            if (Object.keys(configData).length > 0) {
                return { type: 'config', data: configData };
            }

            return { type: 'text', data: text };
        }

        // 从结构化数据中提取API配置
        function extractFromStructuredData(parsedResult) {
            const data = parsedResult.data;
            let serverAddress = '';
            let apiKey = '';

            if (typeof data === 'object' && data !== null) {
                // 常见的服务器地址字段名
                const urlFields = ['baseUrl', 'base_url', 'url', 'endpoint', 'host', 'server', 'api_url', 'api_endpoint'];
                // 常见的API密钥字段名
                const keyFields = ['apiKey', 'api_key', 'key', 'token', 'secret', 'access_token', 'auth_token', 'authorization'];

                // 查找服务器地址
                for (const field of urlFields) {
                    if (data[field] && typeof data[field] === 'string') {
                        serverAddress = data[field];
                        break;
                    }
                }

                // 查找API密钥
                for (const field of keyFields) {
                    if (data[field] && typeof data[field] === 'string') {
                        apiKey = data[field];
                        break;
                    }
                }
            }

            return { serverAddress, apiKey };
        }

        // 自动检测输入文本中的服务器地址和API密钥
        function autoDetect() {
            const inputText = document.getElementById('input-text').value;
            const autoDetectDiv = document.getElementById('auto-detect');

            if (!inputText.trim()) {
                autoDetectDiv.style.display = 'none';
                return;
            }

            let serverAddress = '';
            let apiKey = '';
            let detectedInfo = [];

            // 首先尝试结构化文本解析
            const parsedResult = parseStructuredText(inputText.trim());
            if (parsedResult.type !== 'text') {
                const extracted = extractFromStructuredData(parsedResult);
                if (extracted.serverAddress) {
                    serverAddress = extracted.serverAddress;
                    detectedInfo.push(`🌐 服务器 (${parsedResult.type.toUpperCase()}): ${serverAddress}`);
                }
                if (extracted.apiKey) {
                    apiKey = extracted.apiKey;
                    const displayKey = extracted.apiKey.length > 16 ?
                        `${extracted.apiKey.substring(0, 10)}...${extracted.apiKey.substring(extracted.apiKey.length - 6)}` :
                        extracted.apiKey;
                    detectedInfo.push(`🔑 密钥 (${parsedResult.type.toUpperCase()}): ${displayKey}`);
                }

                // 如果结构化解析成功，直接返回结果
                if (serverAddress || apiKey) {
                    if (serverAddress) {
                        document.getElementById('server-address').value = serverAddress;
                    }
                    if (apiKey) {
                        document.getElementById('api-key').value = apiKey;
                    }

                    autoDetectDiv.innerHTML = `🔍 自动识别: ${detectedInfo.join(', ')}`;
                    autoDetectDiv.style.display = 'block';
                    updateResult();
                    return;
                }
            }

            // 检测服务器地址 - 增强模式
            const urlPatterns = [
                // 标准HTTP/HTTPS URL
                /https?:\/\/[^\s\n\r<>"']+/gi,

                // IP地址 + 端口格式
                /(?:https?:\/\/)?(?:\d{1,3}\.){3}\d{1,3}(?::\d{1,5})?(?:\/[^\s]*)?/gi,

                // 域名 + 端口格式
                /(?:https?:\/\/)?[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*:\d{1,5}(?:\/[^\s]*)*/gi,

                // 带标签的URL（中英文）
                /(?:服务器|server|base_?url|endpoint|host|url|地址|接口)[\s\:：=]*([^\s\n\r<>"']+)/gi,

                // localhost格式
                /(?:https?:\/\/)?localhost(?::\d{1,5})?(?:\/[^\s]*)*/gi,

                // 127.0.0.1格式
                /(?:https?:\/\/)?127\.0\.0\.1(?::\d{1,5})?(?:\/[^\s]*)*/gi,

                // API网关格式
                /[a-zA-Z0-9-]+\.(?:api|gateway|service)\.[a-zA-Z0-9.-]+/gi,
            ];

            for (const pattern of urlPatterns) {
                const matches = inputText.match(pattern);
                if (matches) {
                    let candidate = matches[0];

                    // 清理可能的标签前缀
                    if (pattern.source.includes('服务器|server|base_url|endpoint|host|url|地址|接口')) {
                        const urlMatch = candidate.match(/([^\s\n\r<>"']+)$/);
                        if (urlMatch) candidate = urlMatch[1];
                    }

                    // 标准化URL格式
                    candidate = candidate.trim();

                    // 为没有协议的URL添加https://
                    if (!candidate.match(/^https?:\/\//)) {
                        // 检查是否是IP地址或localhost
                        if (candidate.match(/^(?:\d{1,3}\.){3}\d{1,3}/) ||
                            candidate.startsWith('localhost') ||
                            candidate.startsWith('127.0.0.1')) {
                            candidate = 'http://' + candidate;
                        } else {
                            candidate = 'https://' + candidate;
                        }
                    }

                    // 验证URL格式
                    try {
                        new URL(candidate);
                        serverAddress = candidate;
                        detectedInfo.push(`🌐 服务器: ${serverAddress}`);
                        break;
                    } catch (e) {
                        // 继续尝试下一个模式
                        continue;
                    }
                }
            }

            // 检测API密钥 - 增强模式
            const keyPatterns = [
                // OpenAI格式
                /sk-[a-zA-Z0-9]{20,}/g,

                // Anthropic Claude格式
                /sk-ant-[a-zA-Z0-9_-]{95,}/g,

                // Google AI格式
                /AIza[a-zA-Z0-9_-]{35}/g,

                // Azure OpenAI格式
                /[a-f0-9]{32}/g,

                // Cohere格式
                /[a-zA-Z0-9]{40}/g,

                // Hugging Face格式
                /hf_[a-zA-Z0-9]{34}/g,

                // Bearer token格式
                /Bearer\s+[a-zA-Z0-9\-_\.]{20,}/gi,

                // JWT token格式
                /eyJ[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+/g,

                // 通用长密钥（32字符以上）
                /[a-zA-Z0-9\-_]{32,}/g,

                // 带标签的密钥（中英文）
                /(?:密钥|key|token|api_?key|secret|access_?token|auth|authorization)[\s\:：=]*([a-zA-Z0-9\-_\.]{20,})/gi,

                // Base64格式密钥
                /[A-Za-z0-9+\/]{40,}={0,2}/g,
            ];

            for (const pattern of keyPatterns) {
                const matches = inputText.match(pattern);
                if (matches) {
                    let candidate = matches[0];

                    // 清理可能的标签前缀
                    if (pattern.source.includes('密钥|key|token|secret|access_token|auth|authorization')) {
                        const keyMatch = candidate.match(/([a-zA-Z0-9\-_\.]{20,})$/);
                        if (keyMatch) candidate = keyMatch[1];
                    }

                    // 清理Bearer前缀
                    if (candidate.toLowerCase().startsWith('bearer ')) {
                        candidate = candidate.substring(7);
                    }

                    candidate = candidate.trim();

                    // 验证密钥格式和长度
                    if (candidate.length >= 20) {
                        // 检测密钥类型
                        let keyType = '通用';
                        if (candidate.startsWith('sk-')) {
                            keyType = 'OpenAI';
                        } else if (candidate.startsWith('sk-ant-')) {
                            keyType = 'Anthropic';
                        } else if (candidate.startsWith('AIza')) {
                            keyType = 'Google AI';
                        } else if (candidate.startsWith('hf_')) {
                            keyType = 'Hugging Face';
                        } else if (candidate.match(/^[a-f0-9]{32}$/)) {
                            keyType = 'Azure';
                        } else if (candidate.match(/^eyJ[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+$/)) {
                            keyType = 'JWT Token';
                        }

                        // 如果不是标准格式，尝试转换为OpenAI格式
                        if (!candidate.startsWith('sk-') &&
                            !candidate.startsWith('sk-ant-') &&
                            !candidate.startsWith('AIza') &&
                            !candidate.startsWith('hf_') &&
                            !candidate.match(/^eyJ/) &&
                            candidate.length > 20 &&
                            candidate.match(/^[a-zA-Z0-9\-_]+$/)) {
                            candidate = 'sk-' + candidate;
                            keyType = 'OpenAI (转换)';
                        }

                        apiKey = candidate;
                        const displayKey = candidate.length > 16 ?
                            `${candidate.substring(0, 10)}...${candidate.substring(candidate.length - 6)}` :
                            candidate;
                        detectedInfo.push(`🔑 密钥 (${keyType}): ${displayKey}`);
                        break;
                    }
                }
            }

            // 尝试识别多组配置
            const multiConfigs = detectMultipleConfigs(inputText);

            if (detectedInfo.length > 0) {
                let displayText = `🔍 自动识别: ${detectedInfo.join(', ')}`;

                // 如果检测到多组配置，显示提示
                if (multiConfigs.length > 1) {
                    displayText += `<br><span style="color: #f59e0b;">⚠️ 检测到 ${multiConfigs.length} 组配置，已选择第一组</span>`;
                    displayText += `<br><button onclick="showMultiConfigSelector()" style="margin-top: 8px; padding: 4px 8px; font-size: 12px; background: #3b82f6; color: white; border: none; border-radius: 4px; cursor: pointer;">选择其他配置</button>`;
                }

                autoDetectDiv.innerHTML = displayText;
                autoDetectDiv.style.display = 'block';

                // 自动填充到表单
                if (serverAddress) {
                    document.getElementById('server-address').value = serverAddress;
                }
                if (apiKey) {
                    document.getElementById('api-key').value = apiKey;
                }

                // 自动更新结果
                updateResult();
            } else {
                autoDetectDiv.innerHTML = '❌ 未识别到有效的服务器地址或API密钥';
                autoDetectDiv.style.display = 'block';
            }
        }

        // 检测多组配置
        function detectMultipleConfigs(text) {
            const configs = [];

            // 按行分割文本，寻找可能的配置组合
            const lines = text.split('\n');
            let currentConfig = { serverAddress: '', apiKey: '', lineStart: 0 };

            for (let i = 0; i < lines.length; i++) {
                const line = lines[i].trim();
                if (!line) continue;

                // 检测服务器地址
                const urlPatterns = [
                    /https?:\/\/[^\s\n\r<>"']+/gi,
                    /(?:\d{1,3}\.){3}\d{1,3}(?::\d{1,5})?/gi,
                    /(?:服务器|server|base_?url|endpoint|host|url|地址|接口)[\s\:：=]*([^\s\n\r<>"']+)/gi,
                ];

                for (const pattern of urlPatterns) {
                    const matches = line.match(pattern);
                    if (matches && !currentConfig.serverAddress) {
                        let candidate = matches[0];
                        if (pattern.source.includes('服务器|server|base_url|endpoint|host|url|地址|接口')) {
                            const urlMatch = candidate.match(/([^\s\n\r<>"']+)$/);
                            if (urlMatch) candidate = urlMatch[1];
                        }

                        if (!candidate.match(/^https?:\/\//)) {
                            candidate = candidate.match(/^(?:\d{1,3}\.){3}\d{1,3}/) ||
                                       candidate.startsWith('localhost') ||
                                       candidate.startsWith('127.0.0.1') ?
                                       'http://' + candidate : 'https://' + candidate;
                        }

                        try {
                            new URL(candidate);
                            currentConfig.serverAddress = candidate;
                            if (!currentConfig.lineStart) currentConfig.lineStart = i;
                        } catch (e) {
                            // 忽略无效URL
                        }
                        break;
                    }
                }

                // 检测API密钥
                const keyPatterns = [
                    /sk-[a-zA-Z0-9]{20,}/g,
                    /sk-ant-[a-zA-Z0-9_-]{95,}/g,
                    /AIza[a-zA-Z0-9_-]{35}/g,
                    /hf_[a-zA-Z0-9]{34}/g,
                    /(?:密钥|key|token|api_?key|secret|access_?token|auth|authorization)[\s\:：=]*([a-zA-Z0-9\-_\.]{20,})/gi,
                ];

                for (const pattern of keyPatterns) {
                    const matches = line.match(pattern);
                    if (matches && !currentConfig.apiKey) {
                        let candidate = matches[0];
                        if (pattern.source.includes('密钥|key|token|secret|access_token|auth|authorization')) {
                            const keyMatch = candidate.match(/([a-zA-Z0-9\-_\.]{20,})$/);
                            if (keyMatch) candidate = keyMatch[1];
                        }

                        if (candidate.length >= 20) {
                            currentConfig.apiKey = candidate;
                            if (!currentConfig.lineStart) currentConfig.lineStart = i;
                        }
                        break;
                    }
                }

                // 如果当前配置已完整，保存并开始新配置
                if (currentConfig.serverAddress && currentConfig.apiKey) {
                    configs.push({ ...currentConfig });
                    currentConfig = { serverAddress: '', apiKey: '', lineStart: i + 1 };
                }

                // 检测分隔符，开始新配置
                if (line.match(/^[-=]{3,}|^#{2,}|^\*{3,}|配置\s*[0-9]+|config\s*[0-9]+/i)) {
                    if (currentConfig.serverAddress || currentConfig.apiKey) {
                        configs.push({ ...currentConfig });
                    }
                    currentConfig = { serverAddress: '', apiKey: '', lineStart: i + 1 };
                }
            }

            // 保存最后一个配置
            if (currentConfig.serverAddress || currentConfig.apiKey) {
                configs.push(currentConfig);
            }

            return configs.filter(config => config.serverAddress || config.apiKey);
        }

        // 全局变量存储多组配置
        let detectedConfigs = [];

        // 显示多配置选择器
        function showMultiConfigSelector() {
            const inputText = document.getElementById('input-text').value;
            detectedConfigs = detectMultipleConfigs(inputText);

            if (detectedConfigs.length <= 1) return;

            let selectorHtml = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;" onclick="closeMultiConfigSelector()">
                    <div style="background: white; border-radius: 12px; padding: 24px; max-width: 600px; max-height: 80vh; overflow-y: auto;" onclick="event.stopPropagation()">
                        <h3 style="margin-top: 0; color: #1e293b;">选择配置组合</h3>
                        <p style="color: #64748b; margin-bottom: 20px;">检测到 ${detectedConfigs.length} 组配置，请选择要使用的配置：</p>
            `;

            detectedConfigs.forEach((config, index) => {
                const serverDisplay = config.serverAddress || '未检测到';
                const keyDisplay = config.apiKey ?
                    (config.apiKey.length > 16 ?
                        `${config.apiKey.substring(0, 10)}...${config.apiKey.substring(config.apiKey.length - 6)}` :
                        config.apiKey) : '未检测到';

                selectorHtml += `
                    <div style="border: 1px solid #e2e8f0; border-radius: 8px; padding: 16px; margin-bottom: 12px; cursor: pointer; transition: all 0.2s;"
                         onclick="selectConfig(${index})"
                         onmouseover="this.style.borderColor='#3b82f6'; this.style.backgroundColor='#f8fafc';"
                         onmouseout="this.style.borderColor='#e2e8f0'; this.style.backgroundColor='white';">
                        <div style="font-weight: 600; color: #1e293b; margin-bottom: 8px;">配置 ${index + 1}</div>
                        <div style="font-size: 14px; color: #64748b; margin-bottom: 4px;">🌐 服务器: ${serverDisplay}</div>
                        <div style="font-size: 14px; color: #64748b;">🔑 密钥: ${keyDisplay}</div>
                    </div>
                `;
            });

            selectorHtml += `
                        <div style="text-align: right; margin-top: 20px;">
                            <button onclick="closeMultiConfigSelector()" style="padding: 8px 16px; background: #6b7280; color: white; border: none; border-radius: 6px; cursor: pointer;">取消</button>
                        </div>
                    </div>
                </div>
            `;

            const selectorDiv = document.createElement('div');
            selectorDiv.id = 'multi-config-selector';
            selectorDiv.innerHTML = selectorHtml;
            document.body.appendChild(selectorDiv);
        }

        // 选择配置
        function selectConfig(index) {
            if (index >= 0 && index < detectedConfigs.length) {
                const config = detectedConfigs[index];
                if (config.serverAddress) {
                    document.getElementById('server-address').value = config.serverAddress;
                }
                if (config.apiKey) {
                    document.getElementById('api-key').value = config.apiKey;
                }
                updateResult();
                showToast(`✅ 已选择配置 ${index + 1}`);
            }
            closeMultiConfigSelector();
        }

        // 关闭多配置选择器
        function closeMultiConfigSelector() {
            const selector = document.getElementById('multi-config-selector');
            if (selector) {
                document.body.removeChild(selector);
            }
        }

        // 实时更新结果
        function updateResult() {
            const serverAddress = document.getElementById('server-address').value.trim();
            const apiKey = document.getElementById('api-key').value.trim();
            
            if (serverAddress && apiKey) {
                generateUrlScheme();
            }
        }

        // 生成URL Scheme
        function generateUrlScheme() {
            const serverAddress = document.getElementById('server-address').value.trim();
            const apiKey = document.getElementById('api-key').value.trim();
            const resultContainer = document.getElementById('result-container');

            // 验证输入
            if (!serverAddress || !apiKey) {
                showEnhancedError('请填写完整的服务器地址和API密钥', [
                    '检查输入文本是否包含有效的URL和API密钥',
                    '尝试使用示例数据进行测试',
                    '确保API密钥格式正确（如：sk-开头）'
                ]);
                return;
            }

            // 验证URL格式
            try {
                new URL(serverAddress);
            } catch (e) {
                showEnhancedError('服务器地址格式不正确', [
                    '使用完整的URL格式，如：https://api.example.com',
                    '确保包含协议前缀（http://或https://）',
                    '检查域名或IP地址是否正确'
                ]);
                return;
            }

            // 验证API密钥格式（放宽限制，支持更多格式）
            if (apiKey.length < 10) {
                showEnhancedError('API密钥格式不正确', [
                    'API密钥长度过短，通常至少包含20个字符',
                    '检查密钥是否完整复制',
                    '不同服务商的密钥格式可能不同'
                ]);
                return;
            }

            try {
                // 创建配置对象
                const cherryConfig = {
                    id: 'new-api',
                    baseUrl: serverAddress,
                    apiKey: apiKey
                };

                // JSON字符串
                const configJson = JSON.stringify(cherryConfig);
                
                // Base64编码
                const base64Config = btoa(configJson);
                
                // URL编码
                const encodedConfig = encodeURIComponent(base64Config);

                // 生成URL Scheme
                const urlScheme = `cherrystudio://providers/api-keys?v=1&data=${encodedConfig}`;
                
                // 生成编码前的URL
                const urlSchemeBeforeBase64 = `cherrystudio://providers/api-keys?v=1&data=${configJson}`;

                // 保存到全局变量
                currentConfig = cherryConfig;
                currentUrlScheme = urlScheme;

                // 显示结果
                displayResults(urlScheme, urlSchemeBeforeBase64, configJson, base64Config, encodedConfig);

            } catch (error) {
                showError('生成URL Scheme时发生错误: ' + error.message);
            }
        }

        // 显示生成结果
        function displayResults(finalUrl, beforeBase64Url, jsonString, base64String, encodedString) {
            const resultContainer = document.getElementById('result-container');
            
            resultContainer.innerHTML = `
                <div class="result-title">
                    <span class="status-indicator"></span>
                    ✅ URL Scheme 生成成功
                </div>

                <!-- 标签页 -->
                <div class="tabs">
                    <button class="tab active" onclick="switchTab('final')">🎯 最终结果</button>
                    <button class="tab" onclick="switchTab('original')">📝 编码前内容</button>
                    <button class="tab" onclick="switchTab('process')">🔧 编码过程</button>
                    <button class="tab" onclick="switchTab('config')">⚙️ 配置详情</button>
                </div>

                <!-- 最终URL Scheme -->
                <div id="tab-final" class="tab-content active">
                    <div class="result-section">
                        <div class="result-title">🚀 最终 URL Scheme（可直接使用）</div>
                        <div class="result-content" onclick="selectText(this)">
                            <span class="copy-btn" onclick="event.stopPropagation(); copyToClipboard('${finalUrl.replace(/'/g, "\\'")}', '最终URL')">复制</span>
                            ${finalUrl}
                        </div>
                        <div class="decode-info">
                            💡 此链接包含Base64编码的配置信息，可直接在Cherry Studio中使用
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div style="display: flex; gap: 8px; margin-top: 16px; flex-wrap: wrap;">
                        <button class="btn btn-primary" style="flex: none; padding: 8px 16px; font-size: 14px;" onclick="copyToClipboard('${finalUrl.replace(/'/g, "\\'")}', '最终URL')">
                            📋 复制链接
                        </button>
                        <button class="btn btn-secondary" style="flex: none; padding: 8px 16px; font-size: 14px;" onclick="openUrlScheme('${finalUrl.replace(/'/g, "\\'")}')">
                            🚀 打开应用
                        </button>
                        <button class="btn btn-secondary" style="flex: none; padding: 8px 16px; font-size: 14px;" onclick="validateConfig()">
                            ✅ 验证配置
                        </button>
                    </div>
                </div>

                <!-- 编码前的URL -->
                <div id="tab-original" class="tab-content">
                    <div class="result-section">
                        <div class="result-title">📝 编码前的 URL Scheme（便于理解）</div>
                        <div class="result-content" onclick="selectText(this)">
                            <span class="copy-btn" onclick="event.stopPropagation(); copyToClipboard('${beforeBase64Url.replace(/'/g, "\\'").replace(/"/g, '\\"')}', '编码前URL')">复制</span>
                            ${escapeHtml(beforeBase64Url)}
                        </div>
                        <div class="decode-info">
                            💡 这是未经Base64编码的URL，可以直接看到JSON配置内容，但Cherry Studio无法直接使用
                        </div>
                    </div>
                </div>

                <!-- 编码过程 -->
                <div id="tab-process" class="tab-content">
                    <div class="result-section">
                        <div class="result-title">🔧 编码转换过程</div>
                        
                        <div style="margin-bottom: 16px;">
                            <strong>步骤 1: JSON 配置</strong>
                            <div class="result-content" onclick="selectText(this)">
                                <span class="copy-btn" onclick="event.stopPropagation(); copyToClipboard('${jsonString.replace(/'/g, "\\'")}', 'JSON配置')">复制</span>
                                ${escapeHtml(jsonString)}
                            </div>
                        </div>

                        <div style="margin-bottom: 16px;">
                            <strong>步骤 2: Base64 编码</strong>
                            <div class="result-content" onclick="selectText(this)" style="word-break: break-all;">
                                <span class="copy-btn" onclick="event.stopPropagation(); copyToClipboard('${base64String}', 'Base64编码')">复制</span>
                                ${base64String}
                            </div>
                        </div>

                        <div style="margin-bottom: 16px;">
                            <strong>步骤 3: URL 编码</strong>
                            <div class="result-content" onclick="selectText(this)" style="word-break: break-all;">
                                <span class="copy-btn" onclick="event.stopPropagation(); copyToClipboard('${encodedString}', 'URL编码')">复制</span>
                                ${encodedString}
                            </div>
                        </div>

                        <div class="decode-info">
                            🔄 编码流程: JSON → Base64 → URL编码 → 嵌入URL Scheme
                        </div>
                    </div>
                </div>

                <!-- 配置详情 -->
                <div id="tab-config" class="tab-content">
                    <div class="result-section">
                        <div class="result-title">⚙️ Cherry Studio 配置详情</div>
                        <div class="config-preview">${JSON.stringify(currentConfig, null, 2)}</div>
                        <div class="decode-info">
                            📋 此配置将在 Cherry Studio 中创建一个名为 "new-api" 的 API 提供商
                        </div>
                    </div>
                </div>
            `;
            
            resultContainer.className = 'result-container success';
            resultContainer.style.display = 'block';
        }

        // 切换标签页
        function switchTab(tabName) {
            // 移除所有活动状态
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            
            // 激活选中的标签页
            document.querySelector(`button[onclick="switchTab('${tabName}')"]`).classList.add('active');
            document.getElementById(`tab-${tabName}`).classList.add('active');
        }

        // HTML转义
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 选择文本
        function selectText(element) {
            const range = document.createRange();
            range.selectNodeContents(element);
            const selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(range);
        }

        // 增强的配置验证和建议功能
        function validateConfig() {
            if (!currentConfig) return;

            const checks = [];
            const suggestions = [];

            // 验证服务器地址
            const urlCheck = validateServerUrl(currentConfig.baseUrl);
            checks.push({
                name: '服务器地址格式',
                result: urlCheck.valid,
                message: urlCheck.message,
                suggestion: urlCheck.suggestion
            });

            // 验证API密钥
            const keyCheck = validateApiKey(currentConfig.apiKey);
            checks.push({
                name: 'API 密钥格式',
                result: keyCheck.valid,
                message: keyCheck.message,
                suggestion: keyCheck.suggestion
            });

            // 验证配置完整性
            const completenessCheck = {
                result: currentConfig.id && currentConfig.baseUrl && currentConfig.apiKey,
                message: (currentConfig.id && currentConfig.baseUrl && currentConfig.apiKey) ?
                    '✅ 配置完整' : '❌ 配置缺失'
            };
            checks.push({
                name: '配置完整性',
                result: completenessCheck.result,
                message: completenessCheck.message
            });

            // 安全性检查
            const securityCheck = performSecurityCheck(currentConfig);
            checks.push({
                name: '安全性检查',
                result: securityCheck.secure,
                message: securityCheck.message,
                suggestion: securityCheck.suggestion
            });

            // 兼容性检查
            const compatibilityCheck = checkCompatibility(currentConfig);
            checks.push({
                name: '兼容性检查',
                result: compatibilityCheck.compatible,
                message: compatibilityCheck.message,
                suggestion: compatibilityCheck.suggestion
            });

            // 生成检查结果HTML
            const checkResults = checks.map(check => {
                let html = `<div style="margin-bottom: 12px; padding: 8px; border-left: 3px solid ${check.result ? '#22c55e' : '#ef4444'}; background: ${check.result ? '#f0fdf4' : '#fef2f2'};">`;
                html += `<div style="font-weight: 600; margin-bottom: 4px;">${check.name}</div>`;
                html += `<div style="margin-bottom: 4px;">${check.message}</div>`;
                if (check.suggestion) {
                    html += `<div style="font-size: 12px; color: #6b7280; font-style: italic;">💡 建议: ${check.suggestion}</div>`;
                }
                html += '</div>';
                return html;
            }).join('');

            showToast('🔍 配置验证完成', checkResults, 8000);
        }

        // 验证服务器URL
        function validateServerUrl(url) {
            try {
                const urlObj = new URL(url);

                let message = '✅ URL格式正确';
                let suggestion = '';

                // 检查协议
                if (urlObj.protocol === 'http:') {
                    message += ' (使用HTTP协议)';
                    suggestion = '建议使用HTTPS协议以确保安全性';
                }

                // 检查是否是本地地址
                if (urlObj.hostname === 'localhost' || urlObj.hostname === '127.0.0.1' || urlObj.hostname.startsWith('192.168.')) {
                    suggestion = suggestion ? suggestion + '; 检测到本地地址，确保网络可达性' : '检测到本地地址，确保网络可达性';
                }

                // 检查端口
                if (urlObj.port && (urlObj.port < 1 || urlObj.port > 65535)) {
                    return { valid: false, message: '❌ 端口号无效', suggestion: '端口号应在1-65535范围内' };
                }

                return { valid: true, message, suggestion };
            } catch (e) {
                return { valid: false, message: '❌ URL格式无效', suggestion: '请使用完整的URL格式，如: https://api.example.com' };
            }
        }

        // 验证API密钥
        function validateApiKey(apiKey) {
            if (!apiKey || apiKey.length < 10) {
                return { valid: false, message: '❌ 密钥过短', suggestion: 'API密钥通常至少包含20个字符' };
            }

            let keyType = '未知';
            let valid = true;
            let message = '';
            let suggestion = '';

            if (apiKey.startsWith('sk-')) {
                keyType = 'OpenAI';
                if (apiKey.length < 23) {
                    valid = false;
                    message = '❌ OpenAI密钥长度不足';
                    suggestion = 'OpenAI密钥应至少包含23个字符';
                } else {
                    message = '✅ OpenAI密钥格式正确';
                }
            } else if (apiKey.startsWith('sk-ant-')) {
                keyType = 'Anthropic';
                if (apiKey.length < 100) {
                    suggestion = 'Anthropic密钥通常较长，请确认密钥完整';
                }
                message = '✅ Anthropic密钥格式正确';
            } else if (apiKey.startsWith('AIza')) {
                keyType = 'Google AI';
                message = '✅ Google AI密钥格式正确';
            } else if (apiKey.startsWith('hf_')) {
                keyType = 'Hugging Face';
                message = '✅ Hugging Face密钥格式正确';
            } else if (apiKey.match(/^[a-f0-9]{32}$/)) {
                keyType = 'Azure';
                message = '✅ Azure密钥格式正确';
            } else if (apiKey.match(/^eyJ[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+$/)) {
                keyType = 'JWT Token';
                message = '✅ JWT Token格式正确';
                suggestion = 'JWT Token可能有时效性，请注意更新';
            } else {
                message = '⚠️ 未识别的密钥格式';
                suggestion = '密钥格式未知，请确认是否为有效的API密钥';
            }

            return { valid, message: `${message} (${keyType})`, suggestion };
        }

        // 安全性检查
        function performSecurityCheck(config) {
            const issues = [];

            // 检查是否使用HTTPS
            if (!config.baseUrl.startsWith('https://')) {
                issues.push('未使用HTTPS协议');
            }

            // 检查密钥是否可能泄露
            if (config.apiKey.length < 20) {
                issues.push('密钥长度过短，可能不安全');
            }

            // 检查是否是测试密钥
            if (config.apiKey.includes('test') || config.apiKey.includes('demo') || config.apiKey.includes('example')) {
                issues.push('检测到测试密钥，请使用正式密钥');
            }

            const secure = issues.length === 0;
            const message = secure ? '✅ 安全检查通过' : `⚠️ 发现 ${issues.length} 个安全问题`;
            const suggestion = issues.length > 0 ? issues.join('; ') : '';

            return { secure, message, suggestion };
        }

        // 兼容性检查
        function checkCompatibility(config) {
            const warnings = [];

            // 检查URL路径
            try {
                const url = new URL(config.baseUrl);
                if (url.pathname !== '/' && !url.pathname.includes('/v1') && !url.pathname.includes('/api')) {
                    warnings.push('URL路径可能不标准，常见路径包含/v1或/api');
                }
            } catch (e) {
                // URL已在之前验证过
            }

            // 检查密钥与服务器的匹配性
            if (config.baseUrl.includes('openai') && !config.apiKey.startsWith('sk-')) {
                warnings.push('OpenAI服务器通常使用sk-开头的密钥');
            }

            if (config.baseUrl.includes('anthropic') && !config.apiKey.startsWith('sk-ant-')) {
                warnings.push('Anthropic服务器通常使用sk-ant-开头的密钥');
            }

            const compatible = warnings.length === 0;
            const message = compatible ? '✅ 兼容性检查通过' : `⚠️ 发现 ${warnings.length} 个兼容性提醒`;
            const suggestion = warnings.length > 0 ? warnings.join('; ') : '';

            return { compatible, message, suggestion };
        }

        // 显示错误信息
        function showError(message) {
            const resultContainer = document.getElementById('result-container');
            resultContainer.innerHTML = `
                <div class="result-title">❌ 生成失败</div>
                <div class="error">${message}</div>
            `;
            resultContainer.className = 'result-container';
            resultContainer.style.display = 'block';
        }

        // 复制到剪贴板
        async function copyToClipboard(text, type = '内容') {
            try {
                await navigator.clipboard.writeText(text);
                showToast(`✅ ${type}已复制到剪贴板`);
            } catch (err) {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.opacity = '0';
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showToast(`✅ ${type}已复制到剪贴板`);
            }
        }

        // 打开URL Scheme
        function openUrlScheme(url) {
            window.open(url, '_blank');
            showToast('🚀 正在尝试打开 Cherry Studio...');
        }

        // 清空所有输入
        function clearAll() {
            document.getElementById('input-text').value = '';
            document.getElementById('server-address').value = '';
            document.getElementById('api-key').value = '';
            document.getElementById('result-container').style.display = 'none';
            document.getElementById('auto-detect').style.display = 'none';
            currentConfig = null;
            currentUrlScheme = '';
            showToast('🗑️ 已清空所有内容');
        }

        // 显示提示信息
        function showToast(message, details = '', duration = 3000) {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #1f2937;
                color: white;
                padding: 16px 24px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 500;
                z-index: 1000;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
                max-width: 400px;
                line-height: 1.4;
            `;
            
            let content = message;
            if (details) {
                content += `<div style="margin-top: 8px; font-size: 12px; opacity: 0.9;">${details}</div>`;
            }
            
            toast.innerHTML = content;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(0)';
            }, 100);

            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => document.body.removeChild(toast), 300);
            }, duration);
        }

        // 显示输入提示
        function showInputTips() {
            const tipsDiv = document.getElementById('input-tips');
            if (tipsDiv) {
                tipsDiv.style.display = 'block';
                setTimeout(() => {
                    tipsDiv.style.opacity = '1';
                    tipsDiv.style.transform = 'translateY(0)';
                }, 10);
            }
        }

        // 隐藏输入提示
        function hideInputTips() {
            const tipsDiv = document.getElementById('input-tips');
            if (tipsDiv) {
                setTimeout(() => {
                    tipsDiv.style.display = 'none';
                }, 2000); // 2秒后隐藏
            }
        }

        // 添加实时输入统计
        function updateInputStats() {
            const inputText = document.getElementById('input-text').value;
            const statsDiv = document.getElementById('input-stats');

            if (!statsDiv) return;

            const charCount = inputText.length;
            const lineCount = inputText.split('\n').length;
            const wordCount = inputText.trim() ? inputText.trim().split(/\s+/).length : 0;

            statsDiv.innerHTML = `📊 字符: ${charCount} | 行数: ${lineCount} | 单词: ${wordCount}`;
        }

        // 添加智能建议功能
        function showSmartSuggestions(text) {
            const suggestionsDiv = document.getElementById('smart-suggestions');
            if (!suggestionsDiv) return;

            const suggestions = [];

            // 检测可能的配置格式
            if (text.includes('{') && text.includes('}')) {
                suggestions.push('🔍 检测到JSON格式，将自动解析结构化数据');
            }

            if (text.includes(':') && !text.includes('{')) {
                suggestions.push('🔍 检测到键值对格式，将尝试YAML解析');
            }

            if (text.includes('http') || text.includes('api')) {
                suggestions.push('🌐 检测到URL信息');
            }

            if (text.includes('sk-') || text.includes('key') || text.includes('token')) {
                suggestions.push('🔑 检测到密钥信息');
            }

            if (suggestions.length > 0) {
                suggestionsDiv.innerHTML = suggestions.join('<br>');
                suggestionsDiv.style.display = 'block';
            } else {
                suggestionsDiv.style.display = 'none';
            }
        }

        // 增强的错误处理和用户反馈
        function showEnhancedError(message, suggestions = []) {
            const resultContainer = document.getElementById('result-container');

            let suggestionsHtml = '';
            if (suggestions.length > 0) {
                suggestionsHtml = `
                    <div style="margin-top: 16px; padding: 12px; background: #fffbeb; border: 1px solid #f59e0b; border-radius: 6px;">
                        <div style="font-weight: 600; color: #92400e; margin-bottom: 8px;">💡 建议解决方案：</div>
                        <ul style="margin: 0; padding-left: 20px; color: #92400e;">
                            ${suggestions.map(s => `<li style="margin-bottom: 4px;">${s}</li>`).join('')}
                        </ul>
                    </div>
                `;
            }

            resultContainer.innerHTML = `
                <div class="result-title">❌ 生成失败</div>
                <div class="error">${message}</div>
                ${suggestionsHtml}
                <div style="margin-top: 16px; text-align: center;">
                    <button onclick="showHelpModal()" style="padding: 8px 16px; background: #3b82f6; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px;">
                        📚 查看帮助文档
                    </button>
                </div>
            `;
            resultContainer.className = 'result-container';
            resultContainer.style.display = 'block';
        }

        // 显示帮助模态框
        function showHelpModal() {
            const helpHtml = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;" onclick="closeHelpModal()">
                    <div style="background: white; border-radius: 12px; padding: 24px; max-width: 600px; max-height: 80vh; overflow-y: auto;" onclick="event.stopPropagation()">
                        <h3 style="margin-top: 0; color: #1e293b;">📚 使用帮助</h3>

                        <div style="margin-bottom: 20px;">
                            <h4 style="color: #3b82f6; margin-bottom: 8px;">支持的输入格式：</h4>
                            <div style="background: #f8fafc; padding: 12px; border-radius: 6px; font-family: monospace; font-size: 13px; margin-bottom: 12px;">
                                <strong>JSON格式：</strong><br>
                                {"baseUrl": "https://api.openai.com", "apiKey": "sk-..."}
                            </div>
                            <div style="background: #f8fafc; padding: 12px; border-radius: 6px; font-family: monospace; font-size: 13px; margin-bottom: 12px;">
                                <strong>YAML格式：</strong><br>
                                baseUrl: https://api.openai.com<br>
                                apiKey: sk-...
                            </div>
                            <div style="background: #f8fafc; padding: 12px; border-radius: 6px; font-family: monospace; font-size: 13px; margin-bottom: 12px;">
                                <strong>环境变量格式：</strong><br>
                                API_URL=https://api.openai.com<br>
                                API_KEY=sk-...
                            </div>
                        </div>

                        <div style="margin-bottom: 20px;">
                            <h4 style="color: #3b82f6; margin-bottom: 8px;">支持的API服务商：</h4>
                            <ul style="margin: 0; padding-left: 20px; font-size: 14px;">
                                <li>OpenAI (sk-...)</li>
                                <li>Anthropic Claude (sk-ant-...)</li>
                                <li>Google AI (AIza...)</li>
                                <li>Hugging Face (hf_...)</li>
                                <li>Azure OpenAI (32位十六进制)</li>
                                <li>其他兼容服务</li>
                            </ul>
                        </div>

                        <div style="text-align: right;">
                            <button onclick="closeHelpModal()" style="padding: 8px 16px; background: #6b7280; color: white; border: none; border-radius: 6px; cursor: pointer;">关闭</button>
                        </div>
                    </div>
                </div>
            `;

            const helpDiv = document.createElement('div');
            helpDiv.id = 'help-modal';
            helpDiv.innerHTML = helpHtml;
            document.body.appendChild(helpDiv);
        }

        // 关闭帮助模态框
        function closeHelpModal() {
            const helpModal = document.getElementById('help-modal');
            if (helpModal) {
                document.body.removeChild(helpModal);
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加示例数据按钮组
            const exampleContainer = document.createElement('div');
            exampleContainer.style.cssText = 'margin-bottom: 16px; display: flex; gap: 8px; flex-wrap: wrap;';

            const examples = [
                {
                    name: '📋 OpenAI示例',
                    data: `{
  "baseUrl": "https://api.openai.com/v1",
  "apiKey": "sk-1234567890abcdefghijklmnopqrstuvwxyz123456"
}`
                },
                {
                    name: '🤖 Anthropic示例',
                    data: `baseUrl: https://api.anthropic.com
apiKey: sk-ant-api03-1234567890abcdefghijklmnopqrstuvwxyz1234567890abcdefghijklmnopqrstuvwxyz1234567890abcdef`
                },
                {
                    name: '🌐 自定义API',
                    data: `服务器：https://my-api.example.com:8080/v1
密钥：sk-custom1234567890abcdefghijklmnop
状态：运行中`
                },
                {
                    name: '📝 多组配置',
                    data: `# 配置1 - OpenAI
API_URL=https://api.openai.com/v1
API_KEY=sk-1234567890abcdefghijklmnopqrstuvwxyz123456

# 配置2 - 本地服务
API_URL=http://localhost:8000
API_KEY=sk-local1234567890abcdefghijklmnop

# 配置3 - Azure
API_URL=https://my-resource.openai.azure.com
API_KEY=a1b2c3d4e5f6789012345678901234567`
                }
            ];

            examples.forEach(example => {
                const button = document.createElement('button');
                button.className = 'btn btn-secondary';
                button.style.cssText = 'padding: 6px 12px; font-size: 12px; flex: 1; min-width: 120px;';
                button.textContent = example.name;
                button.onclick = function() {
                    document.getElementById('input-text').value = example.data;
                    autoDetect();
                    showToast(`✅ 已加载${example.name}`);
                };
                exampleContainer.appendChild(button);
            });

            // 添加统计信息显示
            const statsDiv = document.createElement('div');
            statsDiv.id = 'input-stats';
            statsDiv.style.cssText = 'margin-top: 8px; font-size: 12px; color: #6b7280; text-align: right;';

            // 添加智能建议显示
            const suggestionsDiv = document.createElement('div');
            suggestionsDiv.id = 'smart-suggestions';
            suggestionsDiv.style.cssText = 'display: none; margin-top: 8px; padding: 8px; background: #f0fdf4; border: 1px solid #22c55e; border-radius: 6px; font-size: 12px; color: #166534;';

            const firstFormGroup = document.querySelector('.form-group');
            document.querySelector('.form-container').insertBefore(exampleContainer, firstFormGroup);

            // 在输入框后添加统计和建议
            const inputFormGroup = document.querySelector('#input-text').parentNode;
            inputFormGroup.appendChild(statsDiv);
            inputFormGroup.appendChild(suggestionsDiv);

            // 增强输入框事件
            const inputTextarea = document.getElementById('input-text');
            inputTextarea.addEventListener('input', function() {
                updateInputStats();
                showSmartSuggestions(this.value);
            });

            // 初始化统计
            updateInputStats();
        });
    </script>
</body>
</html>
