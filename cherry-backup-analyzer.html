<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cherry Studio 备份文件分析器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .content {
            padding: 30px;
        }

        .input-section {
            margin-bottom: 30px;
        }

        .input-section label {
            display: block;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .input-section textarea {
            width: 100%;
            min-height: 200px;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-family: monospace;
            font-size: 13px;
            resize: vertical;
        }

        .input-section textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .button-group {
            display: flex;
            gap: 12px;
            margin-bottom: 30px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 2px solid #d1d5db;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
        }

        .results {
            display: none;
        }

        .summary {
            background: #f0fdf4;
            border: 1px solid #22c55e;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
        }

        .summary h3 {
            color: #166534;
            margin-bottom: 8px;
        }

        .provider-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
        }

        .provider-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .provider-title {
            font-weight: 600;
            color: #1e293b;
            font-size: 16px;
        }

        .provider-type {
            background: #3b82f6;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }

        .provider-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 12px;
            margin-bottom: 12px;
        }

        .detail-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .detail-label {
            font-weight: 500;
            color: #6b7280;
            min-width: 60px;
        }

        .detail-value {
            color: #1f2937;
            font-family: monospace;
            font-size: 13px;
            word-break: break-all;
        }

        .raw-config {
            background: #1e293b;
            color: #e2e8f0;
            padding: 12px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
            white-space: pre;
        }

        .toggle-raw {
            background: none;
            border: 1px solid #d1d5db;
            color: #6b7280;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .toggle-raw:hover {
            background: #f3f4f6;
        }

        .export-section {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
        }

        .copy-btn {
            background: #10b981;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }

        .copy-btn:hover {
            background: #059669;
        }

        .error {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #991b1b;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 16px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Cherry Studio 备份文件分析器</h1>
            <p>提取和分析模型提供商配置信息</p>
        </div>

        <div class="content">
            <div class="input-section">
                <label for="backup-content">📋 粘贴备份文件内容</label>
                <textarea 
                    id="backup-content" 
                    placeholder="请粘贴您的 Cherry Studio 备份文件内容...

支持格式：
- JSON 格式的备份文件
- 包含转义字符的文本
- 任何包含提供商配置的文本内容"
                ></textarea>
            </div>

            <div class="button-group">
                <button class="btn btn-primary" onclick="analyzeBackup()">
                    🚀 分析备份文件
                </button>
                <button class="btn btn-secondary" onclick="clearAll()">
                    🗑️ 清空
                </button>
                <button class="btn btn-secondary" onclick="loadExample()">
                    📋 加载示例
                </button>
            </div>

            <div id="error-container"></div>

            <div id="results" class="results">
                <div id="summary" class="summary">
                    <h3>📊 分析摘要</h3>
                    <div id="summary-content"></div>
                </div>

                <div id="providers-container"></div>

                <div class="export-section">
                    <h3>📤 导出选项</h3>
                    <div class="button-group">
                        <button class="copy-btn" onclick="exportAsMarkdown()">
                            📝 导出为 Markdown
                        </button>
                        <button class="copy-btn" onclick="exportAsJSON()">
                            📄 导出为 JSON
                        </button>
                        <button class="copy-btn" onclick="exportAsCSV()">
                            📊 导出为 CSV
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="cherry-backup-parser.js"></script>
    <script>
        let currentProviders = [];

        function analyzeBackup() {
            const content = document.getElementById('backup-content').value.trim();
            const errorContainer = document.getElementById('error-container');
            const resultsContainer = document.getElementById('results');

            // 清空错误信息
            errorContainer.innerHTML = '';

            if (!content) {
                showError('请输入备份文件内容');
                return;
            }

            try {
                console.log('开始分析备份文件...');
                
                const providers = parseProviders(content);
                console.log(`发现 ${providers.length} 个提供商配置`);

                if (providers.length === 0) {
                    showError('未找到任何模型提供商配置。请确认备份文件内容正确。');
                    return;
                }

                currentProviders = formatProviders(providers);
                displayResults(currentProviders);
                resultsContainer.style.display = 'block';

            } catch (error) {
                console.error('分析失败:', error);
                showError(`分析失败: ${error.message}`);
            }
        }

        function showError(message) {
            const errorContainer = document.getElementById('error-container');
            errorContainer.innerHTML = `<div class="error">❌ ${message}</div>`;
        }

        function displayResults(providers) {
            // 显示摘要
            const summaryContent = document.getElementById('summary-content');
            const typeCount = {};
            providers.forEach(p => {
                typeCount[p.type] = (typeCount[p.type] || 0) + 1;
            });

            let summaryHtml = `<p>总计发现 <strong>${providers.length}</strong> 个模型提供商配置</p>`;
            summaryHtml += '<p>类型分布：';
            Object.entries(typeCount).forEach(([type, count]) => {
                summaryHtml += ` <span style="background: #e0e7ff; padding: 2px 6px; border-radius: 4px; margin: 0 4px;">${type}: ${count}</span>`;
            });
            summaryHtml += '</p>';

            summaryContent.innerHTML = summaryHtml;

            // 显示提供商列表
            const providersContainer = document.getElementById('providers-container');
            let providersHtml = '';

            providers.forEach(provider => {
                providersHtml += `
                    <div class="provider-card">
                        <div class="provider-header">
                            <div class="provider-title">🔧 ${provider.name}</div>
                            <div class="provider-type">${provider.type}</div>
                        </div>
                        
                        <div class="provider-details">
                            <div class="detail-item">
                                <span class="detail-label">🔑 密钥:</span>
                                <span class="detail-value">${maskApiKey(provider.apiKey)}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">🌐 地址:</span>
                                <span class="detail-value">${provider.apiHost}</span>
                            </div>
                            ${provider.model ? `
                            <div class="detail-item">
                                <span class="detail-label">🤖 模型:</span>
                                <span class="detail-value">${provider.model}</span>
                            </div>
                            ` : ''}
                        </div>
                        
                        <div style="margin-top: 12px;">
                            <button class="toggle-raw" onclick="toggleRawConfig(${provider.index})">
                                📄 查看原始配置
                            </button>
                            <div id="raw-${provider.index}" class="raw-config" style="display: none; margin-top: 8px;">
${JSON.stringify(provider.raw, null, 2)}
                            </div>
                        </div>
                    </div>
                `;
            });

            providersContainer.innerHTML = providersHtml;
        }

        function toggleRawConfig(index) {
            const rawDiv = document.getElementById(`raw-${index}`);
            const button = event.target;
            
            if (rawDiv.style.display === 'none') {
                rawDiv.style.display = 'block';
                button.textContent = '📄 隐藏原始配置';
            } else {
                rawDiv.style.display = 'none';
                button.textContent = '📄 查看原始配置';
            }
        }

        function exportAsMarkdown() {
            if (currentProviders.length === 0) return;
            
            const report = generateReport(currentProviders);
            copyToClipboard(report, 'Markdown 报告');
        }

        function exportAsJSON() {
            if (currentProviders.length === 0) return;
            
            const json = JSON.stringify(currentProviders, null, 2);
            copyToClipboard(json, 'JSON 数据');
        }

        function exportAsCSV() {
            if (currentProviders.length === 0) return;
            
            let csv = 'Index,Name,Type,API Key,API Host,Model\n';
            currentProviders.forEach(p => {
                csv += `${p.index},"${p.name}","${p.type}","${maskApiKey(p.apiKey)}","${p.apiHost}","${p.model}"\n`;
            });
            
            copyToClipboard(csv, 'CSV 数据');
        }

        function copyToClipboard(text, type) {
            navigator.clipboard.writeText(text).then(() => {
                alert(`✅ ${type}已复制到剪贴板`);
            }).catch(err => {
                console.error('复制失败:', err);
                alert('❌ 复制失败，请手动复制');
            });
        }

        function clearAll() {
            document.getElementById('backup-content').value = '';
            document.getElementById('results').style.display = 'none';
            document.getElementById('error-container').innerHTML = '';
            currentProviders = [];
        }

        function loadExample() {
            const example = `{
  "providers": [
    {
      "name": "OpenAI Official",
      "type": "openai",
      "apiKey": "sk-1234567890abcdefghijklmnopqrstuvwxyz123456",
      "apiHost": "https://api.openai.com"
    },
    {
      "name": "Custom API",
      "type": "openai",
      "apiKey": "sk-custom1234567890abcdefghijklmnop",
      "apiHost": "https://my-api.example.com"
    }
  ]
}`;
            document.getElementById('backup-content').value = example;
        }

        // 页面加载时的提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Cherry Studio 备份文件分析器已加载');
        });
    </script>
</body>
</html>
