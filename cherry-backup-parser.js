/**
 * Cherry Studio 备份文件解析器
 * 用于提取模型提供商配置信息
 */

function parseProviders(backupContent) {
    const providers = [];
    
    try {
        // 尝试解析为JSON
        let data;
        if (typeof backupContent === 'string') {
            // 处理可能的转义字符
            const cleanContent = backupContent.replace(/\\\"/g, '"');
            data = JSON.parse(cleanContent);
        } else {
            data = backupContent;
        }
        
        // 查找providers相关的数据
        const findProviders = (obj, path = '') => {
            if (typeof obj !== 'object' || obj === null) return;
            
            for (const [key, value] of Object.entries(obj)) {
                const currentPath = path ? `${path}.${key}` : key;
                
                // 检查是否是provider配置
                if (isProviderConfig(value)) {
                    providers.push({
                        path: currentPath,
                        config: value
                    });
                }
                
                // 递归搜索
                if (typeof value === 'object') {
                    findProviders(value, currentPath);
                }
            }
        };
        
        findProviders(data);
        
    } catch (error) {
        console.error('解析JSON失败，尝试正则表达式提取:', error.message);
        
        // 使用正则表达式提取provider配置
        extractWithRegex(backupContent, providers);
    }
    
    return providers;
}

function isProviderConfig(obj) {
    if (typeof obj !== 'object' || obj === null) return false;
    
    // 检查是否包含provider的关键字段
    const hasName = 'name' in obj;
    const hasType = 'type' in obj;
    const hasApiKey = 'apiKey' in obj || 'api_key' in obj;
    const hasApiHost = 'apiHost' in obj || 'api_host' in obj || 'baseUrl' in obj || 'base_url' in obj;
    
    // 至少需要有name和type，或者apiKey
    return (hasName && hasType) || (hasApiKey && hasApiHost);
}

function extractWithRegex(content, providers) {
    // 正则表达式模式匹配provider配置
    const patterns = [
        // 匹配完整的provider对象
        /\{[^{}]*"name"[^{}]*"type"[^{}]*"apiKey"[^{}]*\}/g,
        /\{[^{}]*"type"[^{}]*"name"[^{}]*"apiKey"[^{}]*\}/g,
        /\{[^{}]*"apiKey"[^{}]*"name"[^{}]*"type"[^{}]*\}/g,
        
        // 匹配包含apiHost的配置
        /\{[^{}]*"apiHost"[^{}]*"apiKey"[^{}]*\}/g,
        /\{[^{}]*"baseUrl"[^{}]*"apiKey"[^{}]*\}/g,
    ];
    
    patterns.forEach(pattern => {
        const matches = content.match(pattern);
        if (matches) {
            matches.forEach(match => {
                try {
                    // 清理转义字符
                    const cleanMatch = match.replace(/\\\"/g, '"');
                    const config = JSON.parse(cleanMatch);
                    
                    if (isProviderConfig(config)) {
                        providers.push({
                            path: 'regex_extracted',
                            config: config
                        });
                    }
                } catch (e) {
                    console.warn('无法解析匹配的配置:', match);
                }
            });
        }
    });
}

function formatProviders(providers) {
    const formatted = [];
    
    providers.forEach((provider, index) => {
        const config = provider.config;
        
        const formattedProvider = {
            index: index + 1,
            name: config.name || config.id || `Provider ${index + 1}`,
            type: config.type || 'unknown',
            apiKey: config.apiKey || config.api_key || '',
            apiHost: config.apiHost || config.api_host || config.baseUrl || config.base_url || '',
            // 其他可能的字段
            model: config.model || config.defaultModel || '',
            temperature: config.temperature || '',
            maxTokens: config.maxTokens || config.max_tokens || '',
            // 原始配置
            raw: config
        };
        
        formatted.push(formattedProvider);
    });
    
    return formatted;
}

function generateReport(providers) {
    let report = `# Cherry Studio 模型提供商配置报告\n\n`;
    report += `总计发现 ${providers.length} 个模型提供商配置\n\n`;
    
    providers.forEach(provider => {
        report += `## 提供商 ${provider.index}\n`;
        report += `- **名称**: ${provider.name}\n`;
        report += `- **类型**: ${provider.type}\n`;
        report += `- **API密钥**: ${maskApiKey(provider.apiKey)}\n`;
        report += `- **API地址**: ${provider.apiHost}\n`;
        
        if (provider.model) {
            report += `- **模型**: ${provider.model}\n`;
        }
        
        if (provider.temperature) {
            report += `- **温度**: ${provider.temperature}\n`;
        }
        
        if (provider.maxTokens) {
            report += `- **最大Token**: ${provider.maxTokens}\n`;
        }
        
        report += `\n**原始配置**:\n`;
        report += `\`\`\`json\n${JSON.stringify(provider.raw, null, 2)}\n\`\`\`\n\n`;
        report += `---\n\n`;
    });
    
    return report;
}

function maskApiKey(apiKey) {
    if (!apiKey || apiKey.length < 8) return apiKey;
    
    const start = apiKey.substring(0, 6);
    const end = apiKey.substring(apiKey.length - 4);
    const middle = '*'.repeat(Math.min(apiKey.length - 10, 20));
    
    return `${start}${middle}${end}`;
}

// 导出函数供使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        parseProviders,
        formatProviders,
        generateReport,
        maskApiKey
    };
}

// 浏览器环境下的使用示例
function processBackupFile(backupContent) {
    console.log('开始解析备份文件...');
    
    const providers = parseProviders(backupContent);
    console.log(`发现 ${providers.length} 个提供商配置`);
    
    const formatted = formatProviders(providers);
    const report = generateReport(formatted);
    
    console.log('解析完成！');
    return {
        providers: formatted,
        report: report
    };
}

// 使用示例
/*
const backupContent = `您的备份文件内容`;
const result = processBackupFile(backupContent);
console.log(result.report);
*/
