<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Cursor注册机</title>
  <style>
    body {
      background: #f5f6fa;
      font-family: 'Segoe UI', <PERSON>l, sans-serif;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      margin: 0;
    }
    .container {
      background: #fff;
      padding: 32px 24px;
      border-radius: 12px;
      box-shadow: 0 2px 16px rgba(0,0,0,0.08);
      min-width: 320px;
      text-align: center;
    }
    h1 {
      margin-bottom: 24px;
      color: #222a3a;
    }
    input[type="text"] {
      width: 80%;
      padding: 10px;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      font-size: 16px;
      margin-bottom: 16px;
    }
    button {
      padding: 10px 24px;
      background: #4f8cff;
      color: #fff;
      border: none;
      border-radius: 6px;
      font-size: 16px;
      cursor: pointer;
      transition: background 0.2s;
    }
    button:hover {
      background: #2563eb;
    }
    .result {
      margin-top: 24px;
      font-size: 18px;
      color: #16a34a;
      word-break: break-all;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Cursor 注册机</h1>
    <input type="text" id="inputInfo" placeholder="请输入邮箱或用户名" />
    <br />
    <button onclick="generateCode()">生成注册码</button>
    <div class="result" id="result"></div>
  </div>
  <script>
    function simpleHash(str) {
      let hash = 0;
      for (let i = 0; i < str.length; i++) {
        hash = ((hash << 5) - hash) + str.charCodeAt(i);
        hash |= 0;
      }
      return Math.abs(hash).toString(16).toUpperCase();
    }
    function generateCode() {
      const input = document.getElementById('inputInfo').value.trim();
      const resultDiv = document.getElementById('result');
      if (!input) {
        resultDiv.textContent = '请输入邮箱或用户名！';
        resultDiv.style.color = '#e11d48';
        return;
      }
      // 简单注册码算法：hash+时间戳后4位
      const code = 'CUR-' + simpleHash(input) + '-' + (Date.now() % 10000).toString().padStart(4, '0');
      resultDiv.textContent = '注册码：' + code;
      resultDiv.style.color = '#16a34a';
    }
  </script>
</body>
</html> 