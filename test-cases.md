# API和密钥识别能力测试用例

## 测试用例1：JSON格式
```json
{
  "baseUrl": "https://api.openai.com/v1",
  "apiKey": "sk-1234567890abcdefghijklmnopqrstuvwxyz123456"
}
```

## 测试用例2：YAML格式
```yaml
baseUrl: https://api.anthropic.com
apiKey: sk-ant-api03-1234567890abcdefghijklmnopqrstuvwxyz1234567890abcdefghijklmnopqrstuvwxyz1234567890abcdef
```

## 测试用例3：环境变量格式
```
API_URL=https://api.openai.com/v1
API_KEY=sk-1234567890abcdefghijklmnopqrstuvwxyz123456
```

## 测试用例4：中文描述
```
服务器地址：https://api.openai.com/v1
API密钥：sk-1234567890abcdefghijklmnopqrstuvwxyz123456
```

## 测试用例5：IP地址和端口
```
服务器：http://*************:8080/v1
密钥：sk-1234567890abcdefghijklmnopqrstuvwxyz123456
```

## 测试用例6：localhost
```
endpoint: http://localhost:3000/api
token: sk-local1234567890abcdefghijklmnop
```

## 测试用例7：Google AI
```
{
  "url": "https://generativelanguage.googleapis.com/v1beta",
  "key": "AIzaSyC1234567890abcdefghijklmnopqrstuvwxyz"
}
```

## 测试用例8：Hugging Face
```
base_url: https://api-inference.huggingface.co
api_key: hf_1234567890abcdefghijklmnopqrstuvwxyz
```

## 测试用例9：Azure OpenAI
```
AZURE_OPENAI_ENDPOINT=https://my-resource.openai.azure.com/
AZURE_OPENAI_API_KEY=a1b2c3d4e5f6789012345678901234567
```

## 测试用例10：多组配置
```
# 配置1 - OpenAI
API_URL=https://api.openai.com/v1
API_KEY=sk-1234567890abcdefghijklmnopqrstuvwxyz123456

---

# 配置2 - 本地服务
server: http://localhost:8000
key: sk-local1234567890abcdefghijklmnop

---

# 配置3 - Anthropic
{
  "baseUrl": "https://api.anthropic.com",
  "apiKey": "sk-ant-api03-abcdefghijklmnopqrstuvwxyz1234567890abcdefghijklmnopqrstuvwxyz1234567890abcdef"
}
```

## 测试用例11：Bearer Token
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
endpoint: https://api.example.com/v1
```

## 测试用例12：配置文件格式
```
[api]
base_url = https://api.openai.com/v1
api_key = sk-1234567890abcdefghijklmnopqrstuvwxyz123456
timeout = 30
```

## 预期结果验证

### URL识别能力
- ✅ 标准HTTP/HTTPS URL
- ✅ IP地址 + 端口
- ✅ localhost格式
- ✅ 域名 + 端口
- ✅ 带路径的URL
- ✅ 中英文标签识别

### API密钥识别能力
- ✅ OpenAI格式 (sk-...)
- ✅ Anthropic格式 (sk-ant-...)
- ✅ Google AI格式 (AIza...)
- ✅ Hugging Face格式 (hf_...)
- ✅ Azure格式 (32位十六进制)
- ✅ JWT Token格式
- ✅ Bearer Token格式
- ✅ 通用长密钥

### 文本解析能力
- ✅ JSON格式解析
- ✅ YAML格式解析
- ✅ 环境变量格式
- ✅ 配置文件格式
- ✅ 中英文标签识别

### 多组配置识别
- ✅ 分隔符识别
- ✅ 配置组合检测
- ✅ 选择器界面
- ✅ 配置切换功能

### 验证和建议功能
- ✅ URL格式验证
- ✅ 密钥格式验证
- ✅ 安全性检查
- ✅ 兼容性检查
- ✅ 智能建议提示
