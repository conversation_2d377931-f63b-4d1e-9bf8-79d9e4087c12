<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API识别功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .test-case {
            background: white;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-title {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
        }
        .test-input {
            background: #f1f5f9;
            padding: 12px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 13px;
            margin-bottom: 12px;
            white-space: pre-wrap;
        }
        .test-result {
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
        }
        .success {
            background: #f0fdf4;
            color: #166534;
            border: 1px solid #22c55e;
        }
        .error {
            background: #fef2f2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        .run-tests {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin-bottom: 20px;
        }
        .summary {
            background: #1e293b;
            color: white;
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>🧪 API和密钥识别功能测试</h1>
    
    <button class="run-tests" onclick="runAllTests()">🚀 运行所有测试</button>
    
    <div id="summary" class="summary" style="display: none;">
        <h3>测试摘要</h3>
        <div id="summary-content"></div>
    </div>
    
    <div id="test-results"></div>

    <script>
        // 测试用例数据
        const testCases = [
            {
                name: "JSON格式 - OpenAI",
                input: `{
  "baseUrl": "https://api.openai.com/v1",
  "apiKey": "sk-1234567890abcdefghijklmnopqrstuvwxyz123456"
}`,
                expectedUrl: "https://api.openai.com/v1",
                expectedKey: "sk-1234567890abcdefghijklmnopqrstuvwxyz123456"
            },
            {
                name: "YAML格式 - Anthropic",
                input: `baseUrl: https://api.anthropic.com
apiKey: sk-ant-api03-1234567890abcdefghijklmnopqrstuvwxyz1234567890abcdefghijklmnopqrstuvwxyz1234567890abcdef`,
                expectedUrl: "https://api.anthropic.com",
                expectedKey: "sk-ant-api03-1234567890abcdefghijklmnopqrstuvwxyz1234567890abcdefghijklmnopqrstuvwxyz1234567890abcdef"
            },
            {
                name: "环境变量格式",
                input: `API_URL=https://api.openai.com/v1
API_KEY=sk-1234567890abcdefghijklmnopqrstuvwxyz123456`,
                expectedUrl: "https://api.openai.com/v1",
                expectedKey: "sk-1234567890abcdefghijklmnopqrstuvwxyz123456"
            },
            {
                name: "中文描述",
                input: `服务器地址：https://api.openai.com/v1
API密钥：sk-1234567890abcdefghijklmnopqrstuvwxyz123456`,
                expectedUrl: "https://api.openai.com/v1",
                expectedKey: "sk-1234567890abcdefghijklmnopqrstuvwxyz123456"
            },
            {
                name: "IP地址和端口",
                input: `服务器：http://*************:8080/v1
密钥：sk-1234567890abcdefghijklmnopqrstuvwxyz123456`,
                expectedUrl: "http://*************:8080/v1",
                expectedKey: "sk-1234567890abcdefghijklmnopqrstuvwxyz123456"
            },
            {
                name: "Google AI格式",
                input: `{
  "url": "https://generativelanguage.googleapis.com/v1beta",
  "key": "AIzaSyC1234567890abcdefghijklmnopqrstuvwxyz"
}`,
                expectedUrl: "https://generativelanguage.googleapis.com/v1beta",
                expectedKey: "AIzaSyC1234567890abcdefghijklmnopqrstuvwxyz"
            },
            {
                name: "Hugging Face格式",
                input: `base_url: https://api-inference.huggingface.co
api_key: hf_1234567890abcdefghijklmnopqrstuvwxyz`,
                expectedUrl: "https://api-inference.huggingface.co",
                expectedKey: "hf_1234567890abcdefghijklmnopqrstuvwxyz"
            }
        ];

        // 从主页面复制的解析函数（简化版）
        function parseStructuredText(text) {
            let parsedData = {};
            
            // 尝试解析JSON
            try {
                parsedData = JSON.parse(text);
                return { type: 'json', data: parsedData };
            } catch (e) {
                // 不是JSON，继续尝试其他格式
            }
            
            // 尝试解析YAML风格的文本
            const yamlPattern = /^(\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:\s*(.+)$/gm;
            let yamlMatch;
            const yamlData = {};
            while ((yamlMatch = yamlPattern.exec(text)) !== null) {
                const key = yamlMatch[2].toLowerCase();
                const value = yamlMatch[3].trim().replace(/^["']|["']$/g, '');
                yamlData[key] = value;
            }
            
            if (Object.keys(yamlData).length > 0) {
                return { type: 'yaml', data: yamlData };
            }
            
            // 尝试解析配置文件格式
            const configPattern = /^([a-zA-Z_][a-zA-Z0-9_]*)\s*[=:]\s*(.+)$/gm;
            let configMatch;
            const configData = {};
            while ((configMatch = configPattern.exec(text)) !== null) {
                const key = configMatch[1].toLowerCase();
                const value = configMatch[2].trim().replace(/^["']|["']$/g, '');
                configData[key] = value;
            }
            
            if (Object.keys(configData).length > 0) {
                return { type: 'config', data: configData };
            }
            
            return { type: 'text', data: text };
        }

        function extractFromStructuredData(parsedResult) {
            const data = parsedResult.data;
            let serverAddress = '';
            let apiKey = '';
            
            if (typeof data === 'object' && data !== null) {
                // 常见的服务器地址字段名
                const urlFields = ['baseUrl', 'base_url', 'url', 'endpoint', 'host', 'server', 'api_url', 'api_endpoint'];
                // 常见的API密钥字段名
                const keyFields = ['apiKey', 'api_key', 'key', 'token', 'secret', 'access_token', 'auth_token', 'authorization'];
                
                // 查找服务器地址
                for (const field of urlFields) {
                    if (data[field] && typeof data[field] === 'string') {
                        serverAddress = data[field];
                        break;
                    }
                }
                
                // 查找API密钥
                for (const field of keyFields) {
                    if (data[field] && typeof data[field] === 'string') {
                        apiKey = data[field];
                        break;
                    }
                }
            }
            
            return { serverAddress, apiKey };
        }

        function testCase(testData) {
            const parsedResult = parseStructuredText(testData.input.trim());
            let serverAddress = '';
            let apiKey = '';
            
            if (parsedResult.type !== 'text') {
                const extracted = extractFromStructuredData(parsedResult);
                serverAddress = extracted.serverAddress;
                apiKey = extracted.apiKey;
            }
            
            // 如果结构化解析失败，尝试正则表达式
            if (!serverAddress || !apiKey) {
                // URL模式
                const urlPatterns = [
                    /https?:\/\/[^\s\n\r<>"']+/gi,
                    /(?:\d{1,3}\.){3}\d{1,3}(?::\d{1,5})?/gi,
                    /(?:服务器|server|base_?url|endpoint|host|url|地址|接口)[\s\:：=]*([^\s\n\r<>"']+)/gi,
                ];
                
                for (const pattern of urlPatterns) {
                    const matches = testData.input.match(pattern);
                    if (matches && !serverAddress) {
                        let candidate = matches[0];
                        if (pattern.source.includes('服务器|server|base_url|endpoint|host|url|地址|接口')) {
                            const urlMatch = candidate.match(/([^\s\n\r<>"']+)$/);
                            if (urlMatch) candidate = urlMatch[1];
                        }
                        
                        if (!candidate.match(/^https?:\/\//)) {
                            candidate = candidate.match(/^(?:\d{1,3}\.){3}\d{1,3}/) || 
                                       candidate.startsWith('localhost') || 
                                       candidate.startsWith('127.0.0.1') ? 
                                       'http://' + candidate : 'https://' + candidate;
                        }
                        
                        try {
                            new URL(candidate);
                            serverAddress = candidate;
                        } catch (e) {
                            // 忽略无效URL
                        }
                        break;
                    }
                }
                
                // 密钥模式
                const keyPatterns = [
                    /sk-[a-zA-Z0-9]{20,}/g,
                    /sk-ant-[a-zA-Z0-9_-]{95,}/g,
                    /AIza[a-zA-Z0-9_-]{35}/g,
                    /hf_[a-zA-Z0-9]{34}/g,
                    /(?:密钥|key|token|api_?key|secret|access_?token|auth|authorization)[\s\:：=]*([a-zA-Z0-9\-_\.]{20,})/gi,
                ];
                
                for (const pattern of keyPatterns) {
                    const matches = testData.input.match(pattern);
                    if (matches && !apiKey) {
                        let candidate = matches[0];
                        if (pattern.source.includes('密钥|key|token|secret|access_token|auth|authorization')) {
                            const keyMatch = candidate.match(/([a-zA-Z0-9\-_\.]{20,})$/);
                            if (keyMatch) candidate = keyMatch[1];
                        }
                        
                        if (candidate.length >= 20) {
                            apiKey = candidate;
                        }
                        break;
                    }
                }
            }
            
            return {
                serverAddress,
                apiKey,
                success: serverAddress === testData.expectedUrl && apiKey === testData.expectedKey
            };
        }

        function runAllTests() {
            const resultsDiv = document.getElementById('test-results');
            const summaryDiv = document.getElementById('summary');
            const summaryContent = document.getElementById('summary-content');
            
            resultsDiv.innerHTML = '';
            let passedTests = 0;
            let totalTests = testCases.length;
            
            testCases.forEach((testData, index) => {
                const result = testCase(testData);
                
                const testDiv = document.createElement('div');
                testDiv.className = 'test-case';
                
                testDiv.innerHTML = `
                    <div class="test-title">测试 ${index + 1}: ${testData.name}</div>
                    <div class="test-input">${testData.input}</div>
                    <div class="test-result ${result.success ? 'success' : 'error'}">
                        ${result.success ? '✅ 通过' : '❌ 失败'}<br>
                        预期URL: ${testData.expectedUrl}<br>
                        实际URL: ${result.serverAddress || '未识别'}<br>
                        预期密钥: ${testData.expectedKey}<br>
                        实际密钥: ${result.apiKey || '未识别'}
                    </div>
                `;
                
                resultsDiv.appendChild(testDiv);
                
                if (result.success) passedTests++;
            });
            
            // 显示摘要
            summaryContent.innerHTML = `
                <div>总测试数: ${totalTests}</div>
                <div>通过测试: ${passedTests}</div>
                <div>失败测试: ${totalTests - passedTests}</div>
                <div>成功率: ${Math.round(passedTests / totalTests * 100)}%</div>
            `;
            summaryDiv.style.display = 'block';
        }
    </script>
</body>
</html>
